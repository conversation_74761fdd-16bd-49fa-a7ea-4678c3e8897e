// Core Trading Types
export interface CandlestickData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

export interface TechnicalIndicators {
  rsi: number;
  ema: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  bollingerBands: {
    upper: number;
    middle: number;
    lower: number;
  };
}

export interface CandlestickPattern {
  name: string;
  type: 'bullish' | 'bearish' | 'neutral';
  strength: number; // 0-100
  detected: boolean;
  timestamp: number;
}

export interface TradingSignal {
  id: string;
  symbol: string;
  timestamp: number;
  type: 'BUY' | 'SELL';
  entryPrice: number;
  stopLoss: number;
  takeProfit: number;
  riskRewardRatio: number;
  confidence: number; // 0-100
  indicators: TechnicalIndicators;
  patterns: CandlestickPattern[];
  reasoning: string;
  status: 'pending' | 'sent' | 'filled' | 'cancelled';
}

// Exness API Types
export interface ExnessWebSocketMessage {
  type: 'tick' | 'heartbeat' | 'error' | 'subscription';
  symbol?: string;
  timestamp?: number;
  bid?: number;
  ask?: number;
  spread?: number;
  volume?: number;
  change?: number;
  changePercent?: number;
}

export interface ExnessSubscription {
  action: 'subscribe' | 'unsubscribe';
  symbols: string[];
  types: string[];
}

export interface ExnessAccount {
  id: string;
  name: string;
  balance: number;
  equity: number;
  margin: number;
  freeMargin: number;
  marginLevel: number;
  currency: string;
  leverage: number;
  serverType: 'demo' | 'real';
}

export interface ExnessPosition {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  volume: number;
  openPrice: number;
  currentPrice: number;
  stopLoss?: number;
  takeProfit?: number;
  profit: number;
  swap: number;
  commission: number;
  openTime: number;
  comment?: string;
}

export interface ExnessOrder {
  id: string;
  symbol: string;
  type: 'buy' | 'sell' | 'buy_limit' | 'sell_limit' | 'buy_stop' | 'sell_stop';
  volume: number;
  price?: number;
  stopLoss?: number;
  takeProfit?: number;
  expiration?: number;
  comment?: string;
  magic?: number;
}

export interface ExnessTradeRequest {
  action: 'deal' | 'pending' | 'sltp' | 'modify' | 'remove';
  symbol: string;
  volume: number;
  type: 'buy' | 'sell' | 'buy_limit' | 'sell_limit' | 'buy_stop' | 'sell_stop';
  price?: number;
  stopLoss?: number;
  takeProfit?: number;
  deviation?: number;
  comment?: string;
  magic?: number;
  expiration?: number;
}

export interface ExnessTradeResult {
  retcode: number;
  deal?: string;
  order?: string;
  volume?: number;
  price?: number;
  bid?: number;
  ask?: number;
  comment?: string;
  request_id?: string;
}

// Configuration Types
export interface TradingConfig {
  symbol: string;
  interval: string;
  maxSignalsPerDay: number;
  minConfidenceLevel: number;
  riskRewardRatio: number;
  maxRiskPerTrade: number;
  stopLossPips: number;
  takeProfitPips: number;
}

export interface IndicatorConfig {
  rsiPeriod: number;
  emaPeriod: number;
  macdFast: number;
  macdSlow: number;
  macdSignal: number;
  bollingerPeriod: number;
  bollingerStdDev: number;
}

// Database Models
export interface SignalDocument extends TradingSignal {
  _id?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface LogDocument {
  _id?: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: Date;
  service: string;
  data?: any;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

// Telegram Types
export interface TelegramMessage {
  chatId: string;
  text: string;
  parseMode?: 'HTML' | 'Markdown';
}

// WebSocket Connection Types
export interface WebSocketConnection {
  isConnected: boolean;
  reconnectAttempts: number;
  lastPingTime: number;
  subscriptions: string[];
}

// Market Data Types
export interface MarketData {
  symbol: string;
  candles: CandlestickData[];
  indicators: TechnicalIndicators;
  patterns: CandlestickPattern[];
  lastUpdate: number;
}

// Price Action Analysis Types
export interface PriceActionAnalysis {
  trend: {
    direction: 'bullish' | 'bearish' | 'sideways';
    strength: number;
    confidence: number;
    timeframe: string;
  };
  keyLevels: {
    support: number[];
    resistance: number[];
    pivots: number[];
  };
  patterns: {
    detected: string[];
    strength: number;
    reliability: number;
  };
  structure: {
    higherHighs: boolean;
    higherLows: boolean;
    lowerHighs: boolean;
    lowerLows: boolean;
  };
}

// Signal Analysis Types
export interface SignalAnalysis {
  priceAction: {
    trend: 'bullish' | 'bearish' | 'sideways';
    strength: number;
    support: number;
    resistance: number;
  };
  ict: {
    orderBlock: boolean;
    fairValueGap: boolean;
    liquidityPool: boolean;
  };
  smc: {
    changeOfCharacter: boolean;
    breakOfStructure: boolean;
    orderFlow: 'bullish' | 'bearish' | 'neutral';
  };
  elliottWave: {
    wave: string;
    degree: string;
    direction: 'impulse' | 'corrective';
  };
  wyckoff: {
    phase: 'accumulation' | 'markup' | 'distribution' | 'markdown';
    position: string;
  };
}

export interface AdvancedSignal extends TradingSignal {
  analysis: SignalAnalysis;
  multiTimeframe: {
    [timeframe: string]: TechnicalIndicators;
  };
}

// Additional signal analysis types
export interface SignalConfluence {
  score: number;
  agreementRatio: number;
  methodologyBreakdown: Record<
    string,
    { agrees: boolean; confidence: number; weight: number }
  >;
  strength: 'weak' | 'moderate' | 'strong' | 'very_strong';
}

export interface EnhancedSignalAnalysis {
  signal: AdvancedSignal;
  confluence: SignalConfluence;
  timeframeAlignment: {
    score: number;
    alignedTimeframes: string[];
    conflictingTimeframes: string[];
    primaryTimeframe: string;
  };
  riskAssessment: RiskAssessment;
  overallConfidence: number;
  recommendation:
    | 'strong_buy'
    | 'buy'
    | 'weak_buy'
    | 'hold'
    | 'weak_sell'
    | 'sell'
    | 'strong_sell';
  metadata: {
    calculatedAt: number;
    methodologyCount: number;
    primaryTimeframe: string;
    riskLevel: string;
  };
}

export interface MethodologySignal {
  methodology: string;
  signal: 'BUY' | 'SELL' | 'NEUTRAL';
  confidence: number;
  strength: number;
  reasoning: string;
  timeframe: string;
  metadata?: any;
}

export interface RiskAssessment {
  score: number;
  factors: Record<string, number>;
  level: 'low' | 'moderate' | 'high' | 'very_high';
  recommendation: 'acceptable' | 'moderate' | 'high_risk';
}

// ICT-specific types
export interface OrderBlock {
  id: string;
  price: number;
  type: 'bullish' | 'bearish';
  strength: number;
  timestamp: number;
  tested: boolean;
  volume: number;
  timeframe?: string;
}

export interface FairValueGap {
  id: string;
  high: number;
  low: number;
  type: 'bullish' | 'bearish';
  timestamp: number;
  filled: boolean;
  strength: number;
  timeframe?: string;
}

export interface LiquidityPool {
  id: string;
  price: number;
  strength: number;
  type: 'buy' | 'sell';
  timestamp: number;
  timeframe?: string;
}

export interface LiquiditySweep {
  id: string;
  direction: 'up' | 'down';
  price: number;
  timestamp: number;
  confirmed: boolean;
  volume: number;
  timeframe?: string;
}

export interface ICTAnalysis {
  orderBlocks: {
    bullish: OrderBlock[];
    bearish: OrderBlock[];
  };
  fairValueGaps: FairValueGap[];
  liquidity: {
    pools: LiquidityPool[];
    sweeps: LiquiditySweep[];
  };
  marketStructure: {
    trend: 'bullish' | 'bearish' | 'ranging';
    higherHighs: boolean;
    higherLows: boolean;
    lowerHighs: boolean;
    lowerLows: boolean;
    structureBreak: boolean;
    confirmation: boolean;
  };
  institutionalLevels: {
    price: number;
    type: 'support' | 'resistance';
    strength: number;
    timestamp: number;
  }[];
  confidence: number;
}

// Wyckoff-specific types
export interface WyckoffEvent {
  id: string;
  type:
    | 'AR'
    | 'LPS'
    | 'SOS'
    | 'SC'
    | 'UTAD'
    | 'SOW'
    | 'LPSY'
    | 'BUEC'
    | 'SELC'
    | 'PS'
    | 'ST'
    | 'BC'
    | 'TR';
  timestamp: number;
  price: number;
  volume: number;
  significance: number;
  confirmed: boolean;
}

export interface WyckoffAnalysis {
  phase: {
    current: 'accumulation' | 'markup' | 'distribution' | 'markdown';
    position: 'early' | 'middle' | 'late';
    confidence: number;
  };
  marketCycle: {
    stage: 'cause' | 'effect' | 'effort' | 'result';
    direction: 'up' | 'down' | 'sideways';
    strength: number;
    description?: string;
    nextExpected?: string;
  };
  supplyDemand: {
    balance: 'supply' | 'demand' | 'equilibrium';
    strength: number;
    volume: number;
  };
  events: WyckoffEvent[];
  priceAction: {
    direction: 'bullish' | 'bearish' | 'neutral';
    strength: number;
    consistency: number;
  };
  volumeSpreadAnalysis?: {
    effort: string;
    result: string;
    relationship: 'harmony' | 'divergence';
    strength: number;
  };
  confidence: number;
}

// Elliott Wave specific types
export interface WaveData {
  start: number;
  end: number;
  timestamp: number;
  type: 'impulse' | 'corrective';
  strength: number;
}

export interface WaveCount {
  wave1: WaveData;
  wave2: WaveData;
  wave3: WaveData;
  wave4: WaveData;
  wave5: WaveData;
  confidence: number;
}

export interface FibonacciLevel {
  level: number;
  price: number;
  type: 'retracement' | 'extension';
  significance: number;
}

export interface WaveProjection {
  targetPrice: number;
  probability: number;
  timeframe: string;
  type: 'wave3' | 'wave5' | 'waveA' | 'waveC';
}

export interface ElliottWaveAnalysis {
  currentWave: {
    number: number;
    type: 'impulse' | 'corrective';
    degree: string;
    direction: 'up' | 'down';
    completion: number;
  };
  waveCount: WaveCount;
  fibonacciLevels: FibonacciLevel[];
  projections: WaveProjection[];
  patterns: {
    impulse: boolean;
    corrective: boolean;
    triangle: boolean;
    flat: boolean;
    zigzag: boolean;
  };
}

// Multi-timeframe types
export interface TimeframeConfig {
  name: string;
  interval: string;
  enabled: boolean;
  weight: number;
  priority: number;
}

export interface MultiTimeframeConfig {
  enabled: boolean;
  timeframes: TimeframeConfig[];
  confluenceThreshold: number;
  higherTimeframeBias: number;
  maxTimeframes: number;
  correlationThreshold: number;
  confluenceRequirement: number;
}

export interface TradingMethodologyConfig {
  priceAction: {
    enabled: boolean;
    weight: number;
    minConfidence: number;
    keyLevelThreshold: number;
    trendStrengthThreshold: number;
  };
  ict: {
    enabled: boolean;
    weight: number;
    minConfidence: number;
    orderBlockMinSize: number;
    fairValueGapMinSize: number;
    liquidityThreshold: number;
  };
  smc: {
    enabled: boolean;
    weight: number;
    minConfidence: number;
    structureBreakThreshold: number;
    orderFlowStrength: number;
    changeOfCharacterThreshold: number;
  };
  elliottWave: {
    enabled: boolean;
    weight: number;
    minConfidence: number;
    waveCountAccuracy: number;
    fibonacciTolerance: number;
    degreeThreshold: number;
  };
  wyckoff: {
    enabled: boolean;
    weight: number;
    minConfidence: number;
    volumeThreshold: number;
    phaseConfidenceThreshold: number;
    eventSignificanceThreshold: number;
  };
}
