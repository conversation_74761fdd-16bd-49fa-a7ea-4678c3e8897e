import dotenv from 'dotenv';
import {
  IndicatorConfig,
  MultiTimeframeConfig,
  TradingConfig,
  TradingMethodologyConfig
} from '../types';

// Load environment variables
dotenv.config();

export interface AppConfig {
  // Server Configuration
  port: number;
  nodeEnv: string;

  // TradingView Configuration
  tradingView: {
    updateInterval: number;
    symbol: string;
    exchange: string;
  };

  // Telegram Configuration
  telegram: {
    botToken: string;
    chatId: string;
  };

  // MongoDB Configuration
  mongodb: {
    uri: string;
    dbName: string;
  };

  // Trading Configuration
  trading: TradingConfig;

  // Indicators Configuration
  indicators: IndicatorConfig;

  // Trading Methodologies Configuration
  methodologies: TradingMethodologyConfig;

  // Multi-Timeframe Configuration
  multiTimeframe: MultiTimeframeConfig;

  // Logging Configuration
  logging: {
    level: string;
    filePath: string;
    rotation: {
      enabled: boolean;
      retentionDays: number;
      baseDirectory: string;
    };
  };

  // WebSocket Configuration
  websocket: {
    reconnectInterval: number;
    maxReconnectAttempts: number;
    pingInterval: number;
  };
}

const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = process.env[key];
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value || defaultValue!;
};

const getEnvNumber = (key: string, defaultValue?: number): number => {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value ? parseInt(value, 10) : defaultValue!;
};

const getEnvFloat = (key: string, defaultValue?: number): number => {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value ? parseFloat(value) : defaultValue!;
};

export const config: AppConfig = {
  port: getEnvNumber('PORT', 3000),
  nodeEnv: getEnvVar('NODE_ENV', 'development'),

  tradingView: {
    updateInterval: getEnvNumber('TRADINGVIEW_UPDATE_INTERVAL', 1000),
    symbol: getEnvVar('TRADINGVIEW_SYMBOL', 'EURUSD'),
    exchange: getEnvVar('TRADINGVIEW_EXCHANGE', 'FX_IDC')
  },

  telegram: {
    botToken: getEnvVar('TELEGRAM_BOT_TOKEN'),
    chatId: getEnvVar('TELEGRAM_CHAT_ID')
  },

  mongodb: {
    uri: getEnvVar(
      'MONGODB_URI',
      'mongodb://localhost:27017/forex-trading-bot'
    ),
    dbName: getEnvVar('MONGODB_DB_NAME', 'forex-trading-bot')
  },

  trading: {
    symbol: getEnvVar('DEFAULT_SYMBOL', 'EUR/USD'),
    interval: getEnvVar('DEFAULT_INTERVAL', '10s'),
    maxSignalsPerDay: getEnvNumber('MAX_SIGNALS_PER_DAY', 50),
    minConfidenceLevel: getEnvNumber('MIN_CONFIDENCE_LEVEL', 75),
    riskRewardRatio: getEnvFloat('DEFAULT_RISK_REWARD_RATIO', 1.5),
    maxRiskPerTrade: getEnvFloat('MAX_RISK_PER_TRADE', 2),
    stopLossPips: getEnvNumber('STOP_LOSS_PIPS', 20),
    takeProfitPips: getEnvNumber('TAKE_PROFIT_PIPS', 30)
  },

  indicators: {
    rsiPeriod: getEnvNumber('RSI_PERIOD', 14),
    emaPeriod: getEnvNumber('EMA_PERIOD', 20),
    macdFast: getEnvNumber('MACD_FAST', 12),
    macdSlow: getEnvNumber('MACD_SLOW', 26),
    macdSignal: getEnvNumber('MACD_SIGNAL', 9),
    bollingerPeriod: getEnvNumber('BOLLINGER_PERIOD', 20),
    bollingerStdDev: getEnvNumber('BOLLINGER_STDDEV', 2)
  },

  methodologies: {
    priceAction: {
      enabled: getEnvVar('PRICE_ACTION_ENABLED', 'true') === 'true',
      weight: getEnvFloat('PRICE_ACTION_WEIGHT', 0.25),
      minConfidence: getEnvNumber('PRICE_ACTION_MIN_CONFIDENCE', 60),
      keyLevelThreshold: getEnvFloat(
        'PRICE_ACTION_KEY_LEVEL_THRESHOLD',
        0.0005
      ),
      trendStrengthThreshold: getEnvFloat('PRICE_ACTION_TREND_STRENGTH', 0.7)
    },
    ict: {
      enabled: getEnvVar('ICT_ENABLED', 'true') === 'true',
      weight: getEnvFloat('ICT_WEIGHT', 0.2),
      minConfidence: getEnvNumber('ICT_MIN_CONFIDENCE', 65),
      orderBlockMinSize: getEnvFloat('ICT_ORDER_BLOCK_MIN_SIZE', 0.0003),
      fairValueGapMinSize: getEnvFloat('ICT_FVG_MIN_SIZE', 0.0002),
      liquidityThreshold: getEnvFloat('ICT_LIQUIDITY_THRESHOLD', 0.8)
    },
    smc: {
      enabled: getEnvVar('SMC_ENABLED', 'true') === 'true',
      weight: getEnvFloat('SMC_WEIGHT', 0.2),
      minConfidence: getEnvNumber('SMC_MIN_CONFIDENCE', 70),
      structureBreakThreshold: getEnvFloat(
        'SMC_STRUCTURE_BREAK_THRESHOLD',
        0.0004
      ),
      orderFlowStrength: getEnvFloat('SMC_ORDER_FLOW_STRENGTH', 0.7),
      changeOfCharacterThreshold: getEnvFloat(
        'SMC_CHARACTER_CHANGE_THRESHOLD',
        0.6
      )
    },
    elliottWave: {
      enabled: getEnvVar('ELLIOTT_WAVE_ENABLED', 'true') === 'true',
      weight: getEnvFloat('ELLIOTT_WAVE_WEIGHT', 0.15),
      minConfidence: getEnvNumber('ELLIOTT_WAVE_MIN_CONFIDENCE', 55),
      waveCountAccuracy: getEnvFloat('ELLIOTT_WAVE_COUNT_ACCURACY', 0.75),
      fibonacciTolerance: getEnvFloat('ELLIOTT_WAVE_FIB_TOLERANCE', 0.02),
      degreeThreshold: getEnvFloat('ELLIOTT_WAVE_DEGREE_THRESHOLD', 0.8)
    },
    wyckoff: {
      enabled: getEnvVar('WYCKOFF_ENABLED', 'true') === 'true',
      weight: getEnvFloat('WYCKOFF_WEIGHT', 0.2),
      minConfidence: getEnvNumber('WYCKOFF_MIN_CONFIDENCE', 65),
      volumeThreshold: getEnvFloat('WYCKOFF_VOLUME_THRESHOLD', 1.5),
      phaseConfidenceThreshold: getEnvFloat('WYCKOFF_PHASE_CONFIDENCE', 0.7),
      eventSignificanceThreshold: getEnvFloat('WYCKOFF_EVENT_SIGNIFICANCE', 0.6)
    }
  },

  multiTimeframe: {
    enabled: getEnvVar('MULTI_TIMEFRAME_ENABLED', 'true') === 'true',
    timeframes: [
      {
        name: 'M1',
        interval: '1m',
        weight: getEnvFloat('M1_WEIGHT', 0.1),
        enabled: getEnvVar('M1_ENABLED', 'true') === 'true',
        priority: 1
      },
      {
        name: 'M5',
        interval: '5m',
        weight: getEnvFloat('M5_WEIGHT', 0.15),
        enabled: getEnvVar('M5_ENABLED', 'true') === 'true',
        priority: 2
      },
      {
        name: 'M15',
        interval: '15m',
        weight: getEnvFloat('M15_WEIGHT', 0.2),
        enabled: getEnvVar('M15_ENABLED', 'true') === 'true',
        priority: 3
      },
      {
        name: 'M30',
        interval: '30m',
        weight: getEnvFloat('M30_WEIGHT', 0.25),
        enabled: getEnvVar('M30_ENABLED', 'true') === 'true',
        priority: 4
      },
      {
        name: 'H1',
        interval: '1h',
        weight: getEnvFloat('H1_WEIGHT', 0.3),
        enabled: getEnvVar('H1_ENABLED', 'true') === 'true',
        priority: 5
      },
      {
        name: 'H4',
        interval: '4h',
        weight: getEnvFloat('H4_WEIGHT', 0.4),
        enabled: getEnvVar('H4_ENABLED', 'true') === 'true',
        priority: 6
      },
      {
        name: 'D1',
        interval: '1d',
        weight: getEnvFloat('D1_WEIGHT', 0.5),
        enabled: getEnvVar('D1_ENABLED', 'true') === 'true',
        priority: 7
      }
    ],
    confluenceThreshold: getEnvFloat('CONFLUENCE_THRESHOLD', 0.6),
    correlationThreshold: getEnvFloat('CORRELATION_THRESHOLD', 0.7),
    higherTimeframeBias: getEnvFloat('HIGHER_TIMEFRAME_BIAS', 1.5),
    confluenceRequirement: getEnvFloat('CONFLUENCE_REQUIREMENT', 0.6),
    maxTimeframes: getEnvNumber('MAX_TIMEFRAMES', 5)
  },

  logging: {
    level: getEnvVar('LOG_LEVEL', 'info'),
    filePath: getEnvVar('LOG_FILE_PATH', './logs/trading-bot.log'),
    rotation: {
      enabled: true,
      retentionDays: getEnvNumber('LOG_RETENTION_DAYS', 30),
      baseDirectory: getEnvVar('LOG_BASE_DIRECTORY', './logs')
    }
  },

  websocket: {
    reconnectInterval: getEnvNumber('WS_RECONNECT_INTERVAL', 5000),
    maxReconnectAttempts: getEnvNumber('WS_MAX_RECONNECT_ATTEMPTS', 10),
    pingInterval: getEnvNumber('WS_PING_INTERVAL', 30000)
  }
};

// Validate critical configuration
export const validateConfig = (): void => {
  const requiredFields = ['telegram.botToken', 'telegram.chatId'];

  for (const field of requiredFields) {
    const keys = field.split('.');
    let value: any = config;

    for (const key of keys) {
      value = value[key];
      if (value === undefined || value === '') {
        throw new Error(
          `Configuration field ${field} is required but not provided`
        );
      }
    }
  }

  console.log('✅ Configuration validation passed');
};

export default config;
