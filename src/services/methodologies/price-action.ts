import { EventEmitter } from 'events';
import { config } from '../../config';
import {
  CandlestickData,
  MethodologySignal,
  PriceActionAnalysis
} from '../../types';
import { logger } from '../../utils/logger';

export interface PriceActionConfig {
  enabled: boolean;
  weight: number;
  minConfidence: number;
  keyLevelThreshold: number;
  trendStrengthThreshold: number;
}

export class PriceActionService extends EventEmitter {
  private config: PriceActionConfig;
  private trendHistory: Map<string, any[]> = new Map();

  constructor() {
    super();
    this.config = config.methodologies.priceAction;
  }

  /**
   * Analyze price action for a given symbol and timeframe
   */
  public async analyze(
    symbol: string,
    candles: CandlestickData[],
    timeframe: string
  ): Promise<PriceActionAnalysis> {
    try {
      if (!this.config.enabled || candles.length < 20) {
        return this.getEmptyAnalysis();
      }

      const analysis: PriceActionAnalysis = {
        trend: await this.analyzeTrend(candles, timeframe),
        keyLevels: await this.identifyKeyLevels(candles),
        patterns: await this.analyzePatterns(candles),
        structure: await this.analyzeStructure(candles)
      };

      // Store analysis for future reference
      this.storeTrendHistory(symbol, analysis.trend);

      logger.trading('debug', `Price Action analysis completed for ${symbol}`, {
        timeframe,
        trend: analysis.trend.direction,
        strength: analysis.trend.strength,
        keyLevels:
          analysis.keyLevels.support.length +
          analysis.keyLevels.resistance.length
      });

      return analysis;
    } catch (error) {
      logger.trading('error', `Price Action analysis failed for ${symbol}`, {
        error
      });
      return this.getEmptyAnalysis();
    }
  }

  /**
   * Generate trading signal based on price action analysis
   */
  public generateSignal(
    symbol: string,
    analysis: PriceActionAnalysis,
    currentPrice: number,
    timeframe: string
  ): MethodologySignal {
    try {
      const signal = this.calculateSignal(analysis, currentPrice);
      const confidence = this.calculateConfidence(analysis, signal);
      const reasoning = this.generateReasoning(analysis, signal);

      return {
        methodology: 'PriceAction',
        confidence,
        signal,
        strength: analysis.trend.strength,
        reasoning,
        timeframe,
        metadata: {
          trend: analysis.trend,
          keyLevels: analysis.keyLevels,
          patterns: analysis.patterns,
          structure: analysis.structure
        }
      };
    } catch (error) {
      logger.trading(
        'error',
        `Price Action signal generation failed for ${symbol}`,
        { error }
      );
      return this.getEmptySignal(timeframe);
    }
  }

  /**
   * Analyze trend direction and strength
   */
  private async analyzeTrend(
    candles: CandlestickData[],
    timeframe: string
  ): Promise<PriceActionAnalysis['trend']> {
    const recentCandles = candles.slice(-20);
    const highs = recentCandles.map((c) => c.high);
    const lows = recentCandles.map((c) => c.low);
    const closes = recentCandles.map((c) => c.close);

    // Calculate trend direction using higher highs/lower lows
    const higherHighs = this.countHigherHighs(highs);
    const lowerLows = this.countLowerLows(lows);
    const higherLows = this.countHigherLows(lows);
    const lowerHighs = this.countLowerHighs(highs);

    let direction: 'bullish' | 'bearish' | 'sideways' = 'sideways';
    let strength = 0;

    if (higherHighs > lowerHighs && higherLows > lowerLows) {
      direction = 'bullish';
      strength = Math.min(
        (higherHighs + higherLows) / (recentCandles.length * 0.4),
        1
      );
    } else if (lowerHighs > higherHighs && lowerLows > higherLows) {
      direction = 'bearish';
      strength = Math.min(
        (lowerHighs + lowerLows) / (recentCandles.length * 0.4),
        1
      );
    } else {
      strength = 0.3; // Sideways market
    }

    // Calculate trend confidence based on consistency
    const priceRange = Math.max(...closes) - Math.min(...closes);
    const avgRange =
      closes.reduce((sum, close, i) => {
        if (i === 0) return sum;
        return sum + Math.abs(close - closes[i - 1]);
      }, 0) /
      (closes.length - 1);

    const confidence = Math.min(
      strength * (priceRange / (avgRange * closes.length)),
      1
    );

    return {
      direction,
      strength,
      confidence,
      timeframe
    };
  }

  /**
   * Identify key support and resistance levels
   */
  private async identifyKeyLevels(
    candles: CandlestickData[]
  ): Promise<PriceActionAnalysis['keyLevels']> {
    const support: number[] = [];
    const resistance: number[] = [];
    const pivots: number[] = [];

    // Use recent 50 candles for level identification
    const recentCandles = candles.slice(-50);
    const highs = recentCandles.map((c) => c.high);
    const lows = recentCandles.map((c) => c.low);

    // Find pivot highs and lows
    for (let i = 2; i < recentCandles.length - 2; i++) {
      const current = recentCandles[i];

      // Pivot high
      if (
        current.high > recentCandles[i - 1].high &&
        current.high > recentCandles[i - 2].high &&
        current.high > recentCandles[i + 1].high &&
        current.high > recentCandles[i + 2].high
      ) {
        resistance.push(current.high);
        pivots.push(current.high);
      }

      // Pivot low
      if (
        current.low < recentCandles[i - 1].low &&
        current.low < recentCandles[i - 2].low &&
        current.low < recentCandles[i + 1].low &&
        current.low < recentCandles[i + 2].low
      ) {
        support.push(current.low);
        pivots.push(current.low);
      }
    }

    // Remove duplicate levels within threshold
    const cleanSupport = this.removeDuplicateLevels(support);
    const cleanResistance = this.removeDuplicateLevels(resistance);

    return {
      support: cleanSupport.slice(-5), // Keep last 5 levels
      resistance: cleanResistance.slice(-5),
      pivots: pivots.slice(-10)
    };
  }

  /**
   * Analyze candlestick patterns
   */
  private async analyzePatterns(
    candles: CandlestickData[]
  ): Promise<PriceActionAnalysis['patterns']> {
    const recentCandles = candles.slice(-10);
    const detected: string[] = [];
    let strength = 0;
    let reliability = 0;

    // Check for common patterns
    if (this.isHammer(recentCandles[recentCandles.length - 1])) {
      detected.push('Hammer');
      strength += 0.3;
      reliability += 0.7;
    }

    if (this.isShootingStar(recentCandles[recentCandles.length - 1])) {
      detected.push('Shooting Star');
      strength += 0.3;
      reliability += 0.7;
    }

    if (this.isEngulfing(recentCandles.slice(-2))) {
      detected.push('Engulfing');
      strength += 0.4;
      reliability += 0.8;
    }

    if (this.isDoji(recentCandles[recentCandles.length - 1])) {
      detected.push('Doji');
      strength += 0.2;
      reliability += 0.5;
    }

    return {
      detected,
      strength: Math.min(strength, 1),
      reliability: Math.min(reliability / detected.length || 0, 1)
    };
  }

  /**
   * Analyze market structure
   */
  private async analyzeStructure(
    candles: CandlestickData[]
  ): Promise<PriceActionAnalysis['structure']> {
    const recentCandles = candles.slice(-20);
    const highs = recentCandles.map((c) => c.high);
    const lows = recentCandles.map((c) => c.low);

    return {
      higherHighs: this.countHigherHighs(highs) > this.countLowerHighs(highs),
      higherLows: this.countHigherLows(lows) > this.countLowerLows(lows),
      lowerHighs: this.countLowerHighs(highs) > this.countHigherHighs(highs),
      lowerLows: this.countLowerLows(lows) > this.countHigherLows(lows)
    };
  }

  // Helper methods
  private countHigherHighs(highs: number[]): number {
    let count = 0;
    for (let i = 1; i < highs.length; i++) {
      if (highs[i] > highs[i - 1]) count++;
    }
    return count;
  }

  private countLowerLows(lows: number[]): number {
    let count = 0;
    for (let i = 1; i < lows.length; i++) {
      if (lows[i] < lows[i - 1]) count++;
    }
    return count;
  }

  private countHigherLows(lows: number[]): number {
    let count = 0;
    for (let i = 1; i < lows.length; i++) {
      if (lows[i] > lows[i - 1]) count++;
    }
    return count;
  }

  private countLowerHighs(highs: number[]): number {
    let count = 0;
    for (let i = 1; i < highs.length; i++) {
      if (highs[i] < highs[i - 1]) count++;
    }
    return count;
  }

  private removeDuplicateLevels(levels: number[]): number[] {
    const threshold = this.config.keyLevelThreshold;
    const unique: number[] = [];

    for (const level of levels) {
      const isDuplicate = unique.some(
        (existing) => Math.abs(level - existing) / existing < threshold
      );
      if (!isDuplicate) {
        unique.push(level);
      }
    }

    return unique.sort((a, b) => a - b);
  }

  private isHammer(candle: CandlestickData): boolean {
    const body = Math.abs(candle.close - candle.open);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    const totalRange = candle.high - candle.low;

    return (
      lowerShadow > body * 2 &&
      upperShadow < body * 0.5 &&
      body > totalRange * 0.1
    );
  }

  private isShootingStar(candle: CandlestickData): boolean {
    const body = Math.abs(candle.close - candle.open);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    const totalRange = candle.high - candle.low;

    return (
      upperShadow > body * 2 &&
      lowerShadow < body * 0.5 &&
      body > totalRange * 0.1
    );
  }

  private isEngulfing(candles: CandlestickData[]): boolean {
    if (candles.length < 2) return false;

    const [prev, current] = candles;
    const prevBody = Math.abs(prev.close - prev.open);
    const currentBody = Math.abs(current.close - current.open);

    return (
      currentBody > prevBody &&
      ((current.close > current.open && prev.close < prev.open) ||
        (current.close < current.open && prev.close > prev.open)) &&
      current.open < Math.min(prev.open, prev.close) &&
      current.close > Math.max(prev.open, prev.close)
    );
  }

  private isDoji(candle: CandlestickData): boolean {
    const body = Math.abs(candle.close - candle.open);
    const totalRange = candle.high - candle.low;
    return body < totalRange * 0.1;
  }

  private calculateSignal(
    analysis: PriceActionAnalysis,
    currentPrice: number
  ): 'BUY' | 'SELL' | 'NEUTRAL' {
    let bullishScore = 0;
    let bearishScore = 0;

    // Trend analysis
    if (analysis.trend.direction === 'bullish') {
      bullishScore += analysis.trend.strength * 0.4;
    } else if (analysis.trend.direction === 'bearish') {
      bearishScore += analysis.trend.strength * 0.4;
    }

    // Support/Resistance analysis
    const nearSupport = analysis.keyLevels.support.some(
      (level) => Math.abs(currentPrice - level) / currentPrice < 0.001
    );
    const nearResistance = analysis.keyLevels.resistance.some(
      (level) => Math.abs(currentPrice - level) / currentPrice < 0.001
    );

    if (nearSupport) bullishScore += 0.3;
    if (nearResistance) bearishScore += 0.3;

    // Pattern analysis
    const bullishPatterns = ['Hammer', 'Bullish Engulfing'];
    const bearishPatterns = ['Shooting Star', 'Bearish Engulfing'];

    if (analysis.patterns.detected.some((p) => bullishPatterns.includes(p))) {
      bullishScore += analysis.patterns.strength * 0.3;
    }
    if (analysis.patterns.detected.some((p) => bearishPatterns.includes(p))) {
      bearishScore += analysis.patterns.strength * 0.3;
    }

    // Structure analysis
    if (analysis.structure.higherHighs && analysis.structure.higherLows) {
      bullishScore += 0.2;
    }
    if (analysis.structure.lowerHighs && analysis.structure.lowerLows) {
      bearishScore += 0.2;
    }

    if (bullishScore > bearishScore && bullishScore > 0.5) {
      return 'BUY';
    } else if (bearishScore > bullishScore && bearishScore > 0.5) {
      return 'SELL';
    }
    return 'NEUTRAL';
  }

  private calculateConfidence(
    analysis: PriceActionAnalysis,
    signal: 'BUY' | 'SELL' | 'NEUTRAL'
  ): number {
    if (signal === 'NEUTRAL') return 0;

    let confidence = 0;

    // Base confidence from trend
    confidence += analysis.trend.confidence * 0.4;

    // Pattern reliability
    confidence += analysis.patterns.reliability * 0.3;

    // Structure consistency
    const structureScore =
      ((analysis.structure.higherHighs ? 1 : 0) +
        (analysis.structure.higherLows ? 1 : 0) +
        (analysis.structure.lowerHighs ? 1 : 0) +
        (analysis.structure.lowerLows ? 1 : 0)) /
      4;
    confidence += structureScore * 0.3;

    return Math.min(confidence * 100, 100);
  }

  private generateReasoning(
    analysis: PriceActionAnalysis,
    signal: 'BUY' | 'SELL' | 'NEUTRAL'
  ): string {
    const reasons: string[] = [];

    if (analysis.trend.direction !== 'sideways') {
      reasons.push(
        `${analysis.trend.direction} trend with ${(
          analysis.trend.strength * 100
        ).toFixed(1)}% strength`
      );
    }

    if (analysis.patterns.detected.length > 0) {
      reasons.push(
        `Detected patterns: ${analysis.patterns.detected.join(', ')}`
      );
    }

    if (
      analysis.keyLevels.support.length > 0 ||
      analysis.keyLevels.resistance.length > 0
    ) {
      reasons.push(
        `Key levels identified: ${analysis.keyLevels.support.length} support, ${analysis.keyLevels.resistance.length} resistance`
      );
    }

    return reasons.join('; ') || 'Price action analysis inconclusive';
  }

  private storeTrendHistory(
    symbol: string,
    trend: PriceActionAnalysis['trend']
  ): void {
    if (!this.trendHistory.has(symbol)) {
      this.trendHistory.set(symbol, []);
    }

    const history = this.trendHistory.get(symbol)!;
    history.push({ ...trend, timestamp: Date.now() });

    // Keep only last 100 entries
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }
  }

  private getEmptyAnalysis(): PriceActionAnalysis {
    return {
      trend: {
        direction: 'sideways',
        strength: 0,
        confidence: 0,
        timeframe: ''
      },
      keyLevels: {
        support: [],
        resistance: [],
        pivots: []
      },
      patterns: {
        detected: [],
        strength: 0,
        reliability: 0
      },
      structure: {
        higherHighs: false,
        higherLows: false,
        lowerHighs: false,
        lowerLows: false
      }
    };
  }

  private getEmptySignal(timeframe: string): MethodologySignal {
    return {
      methodology: 'PriceAction',
      confidence: 0,
      signal: 'NEUTRAL',
      strength: 0,
      reasoning: 'Price Action analysis disabled or insufficient data',
      timeframe,
      metadata: {}
    };
  }
}

// Export singleton instance
export const priceActionService = new PriceActionService();
