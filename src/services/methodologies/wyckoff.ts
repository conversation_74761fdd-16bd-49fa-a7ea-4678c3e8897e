import { EventEmitter } from 'events';
import { config } from '../../config';
import {
  CandlestickData,
  MethodologySignal,
  TechnicalIndicators,
  WyckoffAnalysis,
  WyckoffEvent
} from '../../types';
import { logger } from '../../utils/logger';

export interface WyckoffConfig {
  enabled: boolean;
  weight: number;
  minConfidence: number;
  volumeThreshold: number;
  phaseConfidenceThreshold: number;
}

export class WyckoffService extends EventEmitter {
  private config: WyckoffConfig;
  private phaseHistory: Map<string, any[]> = new Map();
  private eventHistory: Map<string, WyckoffEvent[]> = new Map();

  constructor() {
    super();
    this.config = config.methodologies.wyckoff;
  }

  /**
   * Analyze Wyckoff concepts for a given symbol and timeframe
   */
  public async analyze(
    symbol: string,
    candles: CandlestickData[],
    timeframe: string,
    indicators?: TechnicalIndicators
  ): Promise<WyckoffAnalysis> {
    try {
      if (!this.config.enabled || candles.length < 50) {
        return this.getEmptyAnalysis();
      }

      const analysis: WyckoffAnalysis = {
        phase: await this.identifyMarketPhase(candles),
        volumeSpreadAnalysis: await this.analyzeVolumeSpread(candles),
        marketCycle: await this.analyzeMarketCycle(candles),
        supplyDemand: await this.analyzeSupplyDemand(candles),
        events: await this.identifyWyckoffEvents(candles),
        priceAction: this.analyzePriceActionDirection(candles),
        confidence: 0
      };

      // Store analysis for future reference
      this.storeAnalysisHistory(symbol, analysis);

      logger.trading('debug', `Wyckoff analysis completed for ${symbol}`, {
        timeframe,
        phase: analysis.phase.current,
        position: analysis.phase.position,
        confidence: analysis.phase.confidence,
        events: analysis.events.length
      });

      return analysis;
    } catch (error) {
      logger.trading('error', `Wyckoff analysis failed for ${symbol}`, {
        error
      });
      return this.getEmptyAnalysis();
    }
  }

  /**
   * Generate trading signal based on Wyckoff analysis
   */
  public generateSignal(
    symbol: string,
    analysis: WyckoffAnalysis,
    currentPrice: number,
    timeframe: string
  ): MethodologySignal {
    try {
      const signal = this.calculateSignal(analysis, currentPrice);
      const confidence = this.calculateConfidence(analysis, signal);
      const reasoning = this.generateReasoning(analysis, signal);

      return {
        methodology: 'Wyckoff',
        confidence,
        signal,
        strength: this.calculateStrength(analysis),
        reasoning,
        timeframe,
        metadata: {
          phase: analysis.phase,
          volumeSpreadAnalysis: analysis.volumeSpreadAnalysis,
          marketCycle: analysis.marketCycle,
          supplyDemand: analysis.supplyDemand,
          events: analysis.events
        }
      };
    } catch (error) {
      logger.trading(
        'error',
        `Wyckoff signal generation failed for ${symbol}`,
        { error }
      );
      return this.getEmptySignal(timeframe);
    }
  }

  /**
   * Identify current market phase
   */
  private async identifyMarketPhase(
    candles: CandlestickData[]
  ): Promise<WyckoffAnalysis['phase']> {
    const recentCandles = candles.slice(-100);
    const priceRange = this.calculatePriceRange(recentCandles);
    const volumeProfile = this.analyzeVolumeProfile(recentCandles);
    const trendAnalysis = this.analyzeTrend(recentCandles);

    let phase: 'accumulation' | 'markup' | 'distribution' | 'markdown' =
      'accumulation';
    let position: 'early' | 'middle' | 'late' = 'early';
    let confidence = 0;

    // Accumulation phase: sideways movement with increasing volume
    if (
      priceRange.volatility < 0.02 &&
      volumeProfile.increasing &&
      trendAnalysis.direction === 'sideways'
    ) {
      phase = 'accumulation';
      confidence = Math.min(
        volumeProfile.strength * trendAnalysis.consistency,
        1
      );
      position = this.determinePhasePosition(recentCandles, 'accumulation');
    }
    // Markup phase: upward movement with strong volume
    else if (trendAnalysis.direction === 'up' && volumeProfile.strength > 0.7) {
      phase = 'markup';
      confidence = Math.min(trendAnalysis.strength * volumeProfile.strength, 1);
      position = this.determinePhasePosition(recentCandles, 'markup');
    }
    // Distribution phase: sideways movement at highs with high volume
    else if (
      priceRange.volatility < 0.03 &&
      priceRange.position > 0.8 &&
      volumeProfile.high
    ) {
      phase = 'distribution';
      confidence = Math.min(
        volumeProfile.strength * (1 - priceRange.volatility),
        1
      );
      position = this.determinePhasePosition(recentCandles, 'distribution');
    }
    // Markdown phase: downward movement
    else if (trendAnalysis.direction === 'down') {
      phase = 'markdown';
      confidence = trendAnalysis.strength;
      position = this.determinePhasePosition(recentCandles, 'markdown');
    }

    return {
      current: phase,
      position,
      confidence
    };
  }

  /**
   * Analyze volume spread relationship
   */
  private async analyzeVolumeSpread(
    candles: CandlestickData[]
  ): Promise<WyckoffAnalysis['volumeSpreadAnalysis']> {
    const recentCandles = candles.slice(-20);
    let totalEffort = 0;
    let totalResult = 0;
    let harmonyCount = 0;
    let divergenceCount = 0;

    for (const candle of recentCandles) {
      const volume = candle.volume || 1;
      const spread = candle.high - candle.low;
      const bodySize = Math.abs(candle.close - candle.open);

      // Effort = Volume
      const effort = this.categorizeVolume(volume, recentCandles);

      // Result = Price movement (spread and body)
      const result = this.categorizePriceMovement(
        spread,
        bodySize,
        recentCandles
      );

      totalEffort += this.getNumericValue(effort);
      totalResult += this.getNumericValue(result);

      // Check for harmony or divergence
      if (
        (effort === 'high' && result === 'high') ||
        (effort === 'low' && result === 'low')
      ) {
        harmonyCount++;
      } else if (
        (effort === 'high' && result === 'low') ||
        (effort === 'low' && result === 'high')
      ) {
        divergenceCount++;
      }
    }

    const avgEffort = totalEffort / recentCandles.length;
    const avgResult = totalResult / recentCandles.length;

    return {
      effort: this.getEffortLevel(avgEffort),
      result: this.getResultLevel(avgResult),
      relationship: harmonyCount > divergenceCount ? 'harmony' : 'divergence',
      strength: (avgEffort + avgResult) / 2
    };
  }

  /**
   * Analyze market cycle position
   */
  private async analyzeMarketCycle(
    candles: CandlestickData[]
  ): Promise<WyckoffAnalysis['marketCycle']> {
    const longTermCandles = candles.slice(-200);
    const priceHistory = longTermCandles.map((c) => c.close);
    const volumeHistory = longTermCandles.map((c) => c.volume || 1);

    // Simplified cycle analysis
    const currentPrice = priceHistory[priceHistory.length - 1];
    const maxPrice = Math.max(...priceHistory);
    const minPrice = Math.min(...priceHistory);
    const pricePosition = (currentPrice - minPrice) / (maxPrice - minPrice);

    let stage = 1;
    let description = '';
    let nextExpected = '';

    if (pricePosition < 0.25) {
      stage = 1;
      description = 'Accumulation phase - Smart money buying';
      nextExpected = 'Markup phase';
    } else if (pricePosition < 0.75) {
      stage = 2;
      description = 'Markup phase - Public participation';
      nextExpected = 'Distribution phase';
    } else {
      stage = 3;
      description = 'Distribution phase - Smart money selling';
      nextExpected = 'Markdown phase';
    }

    return {
      stage:
        stage > 2
          ? 'result'
          : stage > 1
          ? 'effort'
          : stage > 0
          ? 'effect'
          : 'cause',
      direction: 'sideways',
      strength: 0.5,
      description,
      nextExpected
    };
  }

  /**
   * Analyze supply and demand balance
   */
  private async analyzeSupplyDemand(
    candles: CandlestickData[]
  ): Promise<WyckoffAnalysis['supplyDemand']> {
    const recentCandles = candles.slice(-50);
    let demandScore = 0;
    let supplyScore = 0;

    for (const candle of recentCandles) {
      const volume = candle.volume || 1;
      const bodySize = Math.abs(candle.close - candle.open);
      const isGreen = candle.close > candle.open;

      // Demand indicators
      if (isGreen && volume > 0) {
        demandScore += volume * (bodySize / (candle.high - candle.low));
      }

      // Supply indicators
      if (!isGreen && volume > 0) {
        supplyScore += volume * (bodySize / (candle.high - candle.low));
      }
    }

    const totalScore = demandScore + supplyScore;
    const demandRatio = totalScore > 0 ? demandScore / totalScore : 0.5;
    const supplyRatio = totalScore > 0 ? supplyScore / totalScore : 0.5;

    let balance: 'supply' | 'demand' | 'equilibrium' = 'equilibrium';
    if (demandRatio > 0.6) {
      balance = 'demand';
    } else if (supplyRatio > 0.6) {
      balance = 'supply';
    }

    const totalVolume = candles.reduce((sum, c) => sum + (c.volume || 1), 0);
    return {
      balance,
      strength: Math.abs(supplyRatio - demandRatio),
      volume: totalVolume
    };
  }

  /**
   * Identify Wyckoff events
   */
  private async identifyWyckoffEvents(
    candles: CandlestickData[]
  ): Promise<WyckoffEvent[]> {
    const events: WyckoffEvent[] = [];
    const recentCandles = candles.slice(-100);

    for (let i = 10; i < recentCandles.length - 10; i++) {
      const candle = recentCandles[i];
      const context = recentCandles.slice(i - 10, i + 11);

      // Preliminary Support (PS)
      if (this.isPreliminarySupport(candle, context)) {
        events.push(this.createWyckoffEvent('PS', candle, context));
      }

      // Selling Climax (SC)
      if (this.isSellingClimax(candle, context)) {
        events.push(this.createWyckoffEvent('SC', candle, context));
      }

      // Automatic Rally (AR)
      if (this.isAutomaticRally(candle, context)) {
        events.push(this.createWyckoffEvent('AR', candle, context));
      }

      // Secondary Test (ST)
      if (this.isSecondaryTest(candle, context)) {
        events.push(this.createWyckoffEvent('ST', candle, context));
      }

      // Backup (BC)
      if (this.isBackup(candle, context)) {
        events.push(this.createWyckoffEvent('BC', candle, context));
      }

      // Last Point of Support (LPS)
      if (this.isLastPointOfSupport(candle, context)) {
        events.push(this.createWyckoffEvent('LPS', candle, context));
      }

      // Sign of Strength (SOS)
      if (this.isSignOfStrength(candle, context)) {
        events.push(this.createWyckoffEvent('SOS', candle, context));
      }
    }

    return events.slice(-10); // Keep last 10 events
  }

  // Helper methods for Wyckoff event identification
  private isPreliminarySupport(
    candle: CandlestickData,
    context: CandlestickData[]
  ): boolean {
    const volume = candle.volume || 1;
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;
    const isLow = context.every((c) => c.low >= candle.low);

    return volume > avgVolume * this.config.volumeThreshold && isLow;
  }

  private isSellingClimax(
    candle: CandlestickData,
    context: CandlestickData[]
  ): boolean {
    const volume = candle.volume || 1;
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;
    const spread = candle.high - candle.low;
    const avgSpread =
      context.reduce((sum, c) => sum + (c.high - c.low), 0) / context.length;
    const isBearish = candle.close < candle.open;

    return volume > avgVolume * 2 && spread > avgSpread * 1.5 && isBearish;
  }

  private isAutomaticRally(
    candle: CandlestickData,
    context: CandlestickData[]
  ): boolean {
    const volume = candle.volume || 1;
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;
    const isBullish = candle.close > candle.open;
    const bodySize = Math.abs(candle.close - candle.open);
    const spread = candle.high - candle.low;

    return volume > avgVolume * 1.5 && isBullish && bodySize > spread * 0.7;
  }

  private isSecondaryTest(
    candle: CandlestickData,
    context: CandlestickData[]
  ): boolean {
    const volume = candle.volume || 1;
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;
    const lows = context.map((c) => c.low);
    const minLow = Math.min(...lows);

    return (
      volume < avgVolume * 0.8 && Math.abs(candle.low - minLow) / minLow < 0.002
    );
  }

  private isBackup(
    candle: CandlestickData,
    context: CandlestickData[]
  ): boolean {
    const volume = candle.volume || 1;
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;
    const isBearish = candle.close < candle.open;

    return volume < avgVolume && isBearish;
  }

  private isLastPointOfSupport(
    candle: CandlestickData,
    context: CandlestickData[]
  ): boolean {
    const volume = candle.volume || 1;
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;
    const isSupport = this.isAtSupportLevel(candle, context);

    return volume > avgVolume && isSupport;
  }

  private isSignOfStrength(
    candle: CandlestickData,
    context: CandlestickData[]
  ): boolean {
    const volume = candle.volume || 1;
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;
    const isBullish = candle.close > candle.open;
    const spread = candle.high - candle.low;
    const avgSpread =
      context.reduce((sum, c) => sum + (c.high - c.low), 0) / context.length;

    return volume > avgVolume * 1.3 && isBullish && spread > avgSpread;
  }

  private isAtSupportLevel(
    candle: CandlestickData,
    context: CandlestickData[]
  ): boolean {
    const lows = context.map((c) => c.low);
    const supportLevels = lows.filter((low, index) =>
      lows
        .slice(0, index)
        .some((prevLow) => Math.abs(low - prevLow) / prevLow < 0.001)
    );

    return supportLevels.some(
      (level) => Math.abs(candle.low - level) / level < 0.001
    );
  }

  private createWyckoffEvent(
    type: WyckoffEvent['type'],
    candle: CandlestickData,
    context: CandlestickData[]
  ): WyckoffEvent {
    const volume = candle.volume || 1;
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;
    const significance = Math.min(volume / avgVolume, 3) / 3;

    return {
      id: `${type}_${candle.timestamp}`,
      type,
      price: candle.close,
      timestamp: candle.timestamp,
      volume,
      significance,
      confirmed: true // Simplified - could add confirmation logic
    };
  }

  // Helper methods
  private calculatePriceRange(candles: CandlestickData[]): {
    volatility: number;
    position: number;
  } {
    const highs = candles.map((c) => c.high);
    const lows = candles.map((c) => c.low);
    const closes = candles.map((c) => c.close);

    const maxHigh = Math.max(...highs);
    const minLow = Math.min(...lows);
    const currentPrice = closes[closes.length - 1];

    const volatility = (maxHigh - minLow) / ((maxHigh + minLow) / 2);
    const position = (currentPrice - minLow) / (maxHigh - minLow);

    return { volatility, position };
  }

  private analyzeVolumeProfile(candles: CandlestickData[]): {
    increasing: boolean;
    strength: number;
    high: boolean;
  } {
    const volumes = candles.map((c) => c.volume || 1);
    const firstHalf = volumes.slice(0, Math.floor(volumes.length / 2));
    const secondHalf = volumes.slice(Math.floor(volumes.length / 2));

    const firstHalfAvg =
      firstHalf.reduce((sum, v) => sum + v, 0) / firstHalf.length;
    const secondHalfAvg =
      secondHalf.reduce((sum, v) => sum + v, 0) / secondHalf.length;
    const totalAvg = volumes.reduce((sum, v) => sum + v, 0) / volumes.length;

    const increasing = secondHalfAvg > firstHalfAvg;
    const strength = Math.abs(secondHalfAvg - firstHalfAvg) / firstHalfAvg;
    const high = secondHalfAvg > totalAvg * 1.2;

    return { increasing, strength, high };
  }

  private analyzeTrend(candles: CandlestickData[]): {
    direction: 'up' | 'down' | 'sideways';
    strength: number;
    consistency: number;
  } {
    const closes = candles.map((c) => c.close);
    const firstPrice = closes[0];
    const lastPrice = closes[closes.length - 1];
    const priceChange = (lastPrice - firstPrice) / firstPrice;

    let direction: 'up' | 'down' | 'sideways' = 'sideways';
    if (priceChange > 0.02) direction = 'up';
    else if (priceChange < -0.02) direction = 'down';

    const strength = Math.abs(priceChange);

    // Calculate consistency
    let consistentMoves = 0;
    for (let i = 1; i < closes.length; i++) {
      const move = closes[i] - closes[i - 1];
      if (
        (direction === 'up' && move > 0) ||
        (direction === 'down' && move < 0)
      ) {
        consistentMoves++;
      }
    }
    const consistency = consistentMoves / (closes.length - 1);

    return { direction, strength, consistency };
  }

  /**
   * Analyze price action direction for Wyckoff analysis
   */
  private analyzePriceActionDirection(candles: CandlestickData[]): {
    direction: 'bullish' | 'bearish' | 'neutral';
    strength: number;
    consistency: number;
  } {
    const recentCandles = candles.slice(-20);
    const priceChanges = recentCandles.map((candle, i) => {
      if (i === 0) return 0;
      return (
        (candle.close - recentCandles[i - 1].close) / recentCandles[i - 1].close
      );
    });

    const avgChange =
      priceChanges.reduce((sum, change) => sum + change, 0) /
      priceChanges.length;
    const volatility = Math.sqrt(
      priceChanges.reduce(
        (sum, change) => sum + Math.pow(change - avgChange, 2),
        0
      ) / priceChanges.length
    );

    let direction: 'bullish' | 'bearish' | 'neutral' = 'neutral';
    if (avgChange > 0.001) direction = 'bullish';
    else if (avgChange < -0.001) direction = 'bearish';

    const strength = Math.abs(avgChange) * 100;
    const consistency = 1 - volatility;

    return { direction, strength, consistency };
  }

  private determinePhasePosition(
    candles: CandlestickData[],
    phase: string
  ): 'early' | 'middle' | 'late' {
    // Simplified logic - could be enhanced with more sophisticated analysis
    const recentVolume =
      candles.slice(-10).reduce((sum, c) => sum + (c.volume || 1), 0) / 10;
    const avgVolume =
      candles.reduce((sum, c) => sum + (c.volume || 1), 0) / candles.length;

    if (recentVolume > avgVolume * 1.5) return 'late';
    if (recentVolume > avgVolume * 1.2) return 'middle';
    return 'early';
  }

  private categorizeVolume(
    volume: number,
    context: CandlestickData[]
  ): 'high' | 'medium' | 'low' {
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;

    if (volume > avgVolume * 1.5) return 'high';
    if (volume > avgVolume * 0.8) return 'medium';
    return 'low';
  }

  private categorizePriceMovement(
    spread: number,
    bodySize: number,
    context: CandlestickData[]
  ): 'high' | 'medium' | 'low' {
    const avgSpread =
      context.reduce((sum, c) => sum + (c.high - c.low), 0) / context.length;
    const avgBody =
      context.reduce((sum, c) => sum + Math.abs(c.close - c.open), 0) /
      context.length;

    const spreadRatio = spread / avgSpread;
    const bodyRatio = bodySize / avgBody;
    const combinedRatio = (spreadRatio + bodyRatio) / 2;

    if (combinedRatio > 1.3) return 'high';
    if (combinedRatio > 0.8) return 'medium';
    return 'low';
  }

  private getNumericValue(level: 'high' | 'medium' | 'low'): number {
    switch (level) {
      case 'high':
        return 3;
      case 'medium':
        return 2;
      case 'low':
        return 1;
    }
  }

  private getEffortLevel(value: number): 'high' | 'medium' | 'low' {
    if (value > 2.5) return 'high';
    if (value > 1.5) return 'medium';
    return 'low';
  }

  private getResultLevel(value: number): 'high' | 'medium' | 'low' {
    if (value > 2.5) return 'high';
    if (value > 1.5) return 'medium';
    return 'low';
  }

  private calculateSignal(
    analysis: WyckoffAnalysis,
    currentPrice: number
  ): 'BUY' | 'SELL' | 'NEUTRAL' {
    let bullishScore = 0;
    let bearishScore = 0;

    // Phase analysis
    if (
      analysis.phase.current === 'accumulation' &&
      analysis.phase.position === 'late'
    ) {
      bullishScore += 0.4;
    } else if (
      analysis.phase.current === 'markup' &&
      analysis.phase.position !== 'late'
    ) {
      bullishScore += 0.3;
    } else if (
      analysis.phase.current === 'distribution' &&
      analysis.phase.position === 'late'
    ) {
      bearishScore += 0.4;
    } else if (analysis.phase.current === 'markdown') {
      bearishScore += 0.3;
    }

    // Volume spread analysis
    if (analysis.volumeSpreadAnalysis) {
      if (analysis.volumeSpreadAnalysis.relationship === 'harmony') {
        if (
          analysis.volumeSpreadAnalysis.effort === 'high' &&
          analysis.volumeSpreadAnalysis.result === 'high'
        ) {
          bullishScore += 0.2;
        }
      } else {
        // Divergence can signal reversal
        if (
          analysis.volumeSpreadAnalysis.effort === 'high' &&
          analysis.volumeSpreadAnalysis.result === 'low'
        ) {
          bearishScore += 0.2;
        }
      }
    }

    // Supply/Demand balance
    if (analysis.supplyDemand.balance === 'demand') {
      bullishScore += 0.2;
    } else if (analysis.supplyDemand.balance === 'supply') {
      bearishScore += 0.2;
    }

    // Recent Wyckoff events
    const recentEvents = analysis.events.filter(
      (event) => Date.now() - event.timestamp < 24 * 60 * 60 * 1000 // Last 24 hours
    );

    const bullishEvents = recentEvents.filter((event) =>
      ['AR', 'LPS', 'SOS'].includes(event.type)
    );
    const bearishEvents = recentEvents.filter((event) =>
      ['SC', 'UTAD', 'SOW'].includes(event.type)
    );

    if (bullishEvents.length > bearishEvents.length) {
      bullishScore += 0.2;
    } else if (bearishEvents.length > bullishEvents.length) {
      bearishScore += 0.2;
    }

    if (bullishScore > bearishScore && bullishScore > 0.6) {
      return 'BUY';
    } else if (bearishScore > bullishScore && bearishScore > 0.6) {
      return 'SELL';
    }
    return 'NEUTRAL';
  }

  private calculateConfidence(
    analysis: WyckoffAnalysis,
    signal: 'BUY' | 'SELL' | 'NEUTRAL'
  ): number {
    if (signal === 'NEUTRAL') return 0;

    let confidence = 0;

    // Phase confidence
    confidence += analysis.phase.confidence * 40;

    // Volume spread relationship
    if (analysis.volumeSpreadAnalysis) {
      if (analysis.volumeSpreadAnalysis.relationship === 'harmony') {
        confidence += 20;
      } else {
        confidence += 10; // Divergence can also be significant
      }
    }

    // Supply/demand clarity
    if (analysis.supplyDemand.balance !== 'equilibrium') {
      confidence += analysis.supplyDemand.strength * 20;
    }

    // Event confirmation
    const significantEvents = analysis.events.filter(
      (event) => event.significance > 0.7
    );
    confidence += Math.min(significantEvents.length * 5, 20);

    return Math.min(confidence, 100);
  }

  private calculateStrength(analysis: WyckoffAnalysis): number {
    let strength = 0;

    // Phase strength
    strength += analysis.phase.confidence * 0.3;

    // Volume spread strength
    if (analysis.volumeSpreadAnalysis) {
      const effortResult =
        this.getNumericValue(
          analysis.volumeSpreadAnalysis.effort as 'high' | 'medium' | 'low'
        ) +
        this.getNumericValue(
          analysis.volumeSpreadAnalysis.result as 'high' | 'medium' | 'low'
        );
      strength += (effortResult / 6) * 0.3; // Normalize to 0-1
    }

    // Supply/demand imbalance
    strength += analysis.supplyDemand.strength * 0.2;

    // Event significance
    const avgEventSignificance =
      analysis.events.reduce((sum, event) => sum + event.significance, 0) /
      (analysis.events.length || 1);
    strength += avgEventSignificance * 0.2;

    return Math.min(strength, 1);
  }

  private generateReasoning(
    analysis: WyckoffAnalysis,
    signal: 'BUY' | 'SELL' | 'NEUTRAL'
  ): string {
    const reasons: string[] = [];

    reasons.push(
      `Market phase: ${analysis.phase.current} (${analysis.phase.position})`
    );
    reasons.push(
      `Phase confidence: ${(analysis.phase.confidence * 100).toFixed(1)}%`
    );

    if (analysis.volumeSpreadAnalysis) {
      reasons.push(
        `Volume-spread: ${analysis.volumeSpreadAnalysis.effort} effort, ${analysis.volumeSpreadAnalysis.result} result (${analysis.volumeSpreadAnalysis.relationship})`
      );
    }

    reasons.push(`Supply/demand balance: ${analysis.supplyDemand.balance}`);

    if (analysis.events.length > 0) {
      const recentEvents = analysis.events.slice(-3).map((e) => e.type);
      reasons.push(`Recent events: ${recentEvents.join(', ')}`);
    }

    return reasons.join('; ') || 'Wyckoff analysis inconclusive';
  }

  private storeAnalysisHistory(
    symbol: string,
    analysis: WyckoffAnalysis
  ): void {
    // Store phase history
    if (!this.phaseHistory.has(symbol)) {
      this.phaseHistory.set(symbol, []);
    }
    const phaseHistory = this.phaseHistory.get(symbol)!;
    phaseHistory.push({ ...analysis.phase, timestamp: Date.now() });
    if (phaseHistory.length > 100) {
      phaseHistory.splice(0, phaseHistory.length - 100);
    }

    // Store event history
    if (!this.eventHistory.has(symbol)) {
      this.eventHistory.set(symbol, []);
    }
    const eventHistory = this.eventHistory.get(symbol)!;
    eventHistory.push(...analysis.events);
    if (eventHistory.length > 50) {
      eventHistory.splice(0, eventHistory.length - 50);
    }
  }

  private getEmptyAnalysis(): WyckoffAnalysis {
    return {
      phase: {
        current: 'accumulation',
        position: 'early',
        confidence: 0
      },
      volumeSpreadAnalysis: {
        effort: 'medium',
        result: 'medium',
        relationship: 'harmony',
        strength: 0.5
      },
      marketCycle: {
        stage: 'cause',
        direction: 'sideways',
        strength: 0.5,
        description: 'Unknown cycle position',
        nextExpected: 'Unknown'
      },
      supplyDemand: {
        balance: 'equilibrium',
        strength: 0.5,
        volume: 1000
      },
      events: [],
      priceAction: {
        direction: 'neutral',
        strength: 0.5,
        consistency: 0.5
      },
      confidence: 0
    };
  }

  private getEmptySignal(timeframe: string): MethodologySignal {
    return {
      methodology: 'Wyckoff',
      confidence: 0,
      signal: 'NEUTRAL',
      strength: 0,
      reasoning: 'Wyckoff analysis disabled or insufficient data',
      timeframe,
      metadata: {}
    };
  }
}

// Export singleton instance
export const wyckoffService = new WyckoffService();
