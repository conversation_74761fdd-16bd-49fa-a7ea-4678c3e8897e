import { EventEmitter } from 'events';
import { config } from '../../config';
import {
  CandlestickData,
  ElliottWaveAnalysis,
  FibonacciLevel,
  MethodologySignal,
  TechnicalIndicators,
  WaveCount,
  WaveData,
  WaveProjection
} from '../../types';
import { logger } from '../../utils/logger';

export interface ElliottWaveConfig {
  enabled: boolean;
  weight: number;
  minConfidence: number;
  waveCountAccuracy: number;
  fibonacciTolerance: number;
}

export class ElliottWaveService extends EventEmitter {
  private config: ElliottWaveConfig;
  private waveHistory: Map<string, WaveData[]> = new Map();
  private fibonacciLevels = [
    0.236, 0.382, 0.5, 0.618, 0.786, 1.0, 1.272, 1.618, 2.618
  ];

  constructor() {
    super();
    this.config = config.methodologies.elliottWave;
  }

  /**
   * Analyze Elliott Wave patterns for a given symbol and timeframe
   */
  public async analyze(
    symbol: string,
    candles: CandlestickData[],
    timeframe: string,
    indicators?: TechnicalIndicators
  ): Promise<ElliottWaveAnalysis> {
    try {
      if (!this.config.enabled || candles.length < 50) {
        return this.getEmptyAnalysis();
      }

      const swingPoints = this.identifySwingPoints(candles);
      const waveCount = await this.performWaveCount(swingPoints);
      const currentWave = this.identifyCurrentWave(waveCount, swingPoints);
      const fibonacciLevels = this.calculateFibonacciLevels(waveCount);
      const projections = this.calculateWaveProjections(waveCount, timeframe);
      const patterns = this.identifyWavePatterns(waveCount);

      const analysis: ElliottWaveAnalysis = {
        currentWave,
        waveCount,
        fibonacciLevels,
        projections,
        patterns
      };

      // Store analysis for future reference
      this.storeWaveHistory(symbol, waveCount);

      logger.trading('debug', `Elliott Wave analysis completed for ${symbol}`, {
        timeframe,
        currentWave: currentWave.number,
        waveType: currentWave.type,
        confidence: waveCount.confidence,
        projections: projections.length
      });

      return analysis;
    } catch (error) {
      logger.trading('error', `Elliott Wave analysis failed for ${symbol}`, {
        error
      });
      return this.getEmptyAnalysis();
    }
  }

  /**
   * Generate trading signal based on Elliott Wave analysis
   */
  public generateSignal(
    symbol: string,
    analysis: ElliottWaveAnalysis,
    currentPrice: number,
    timeframe: string
  ): MethodologySignal {
    try {
      const signal = this.calculateSignal(analysis, currentPrice);
      const confidence = this.calculateConfidence(analysis, signal);
      const reasoning = this.generateReasoning(analysis, signal);

      return {
        methodology: 'ElliottWave',
        confidence,
        signal,
        strength: this.calculateStrength(analysis),
        reasoning,
        timeframe,
        metadata: {
          currentWave: analysis.currentWave,
          waveCount: analysis.waveCount,
          fibonacciLevels: analysis.fibonacciLevels,
          projections: analysis.projections,
          patterns: analysis.patterns
        }
      };
    } catch (error) {
      logger.trading(
        'error',
        `Elliott Wave signal generation failed for ${symbol}`,
        { error }
      );
      return this.getEmptySignal(timeframe);
    }
  }

  /**
   * Identify swing points for wave analysis
   */
  private identifySwingPoints(
    candles: CandlestickData[]
  ): Array<{
    price: number;
    timestamp: number;
    type: 'high' | 'low';
    index: number;
  }> {
    const swingPoints: Array<{
      price: number;
      timestamp: number;
      type: 'high' | 'low';
      index: number;
    }> = [];
    const lookback = 5;

    for (let i = lookback; i < candles.length - lookback; i++) {
      const current = candles[i];
      const before = candles.slice(i - lookback, i);
      const after = candles.slice(i + 1, i + lookback + 1);

      // Swing high
      if (
        before.every((c) => c.high < current.high) &&
        after.every((c) => c.high < current.high)
      ) {
        swingPoints.push({
          price: current.high,
          timestamp: current.timestamp,
          type: 'high',
          index: i
        });
      }

      // Swing low
      if (
        before.every((c) => c.low > current.low) &&
        after.every((c) => c.low > current.low)
      ) {
        swingPoints.push({
          price: current.low,
          timestamp: current.timestamp,
          type: 'low',
          index: i
        });
      }
    }

    return swingPoints.sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * Perform Elliott Wave count
   */
  private async performWaveCount(
    swingPoints: Array<{
      price: number;
      timestamp: number;
      type: 'high' | 'low';
      index: number;
    }>
  ): Promise<WaveCount> {
    if (swingPoints.length < 10) {
      return this.getEmptyWaveCount();
    }

    // Take the most recent significant swing points
    const recentPoints = swingPoints.slice(-10);

    // Attempt to identify 5-wave structure
    const waves = this.identifyFiveWaveStructure(recentPoints);

    if (waves.length >= 5) {
      const confidence = this.calculateWaveCountConfidence(waves);

      return {
        wave1: waves[0],
        wave2: waves[1],
        wave3: waves[2],
        wave4: waves[3],
        wave5: waves[4],
        confidence
      };
    }

    return this.getEmptyWaveCount();
  }

  /**
   * Identify five-wave structure
   */
  private identifyFiveWaveStructure(
    swingPoints: Array<{
      price: number;
      timestamp: number;
      type: 'high' | 'low';
    }>
  ): WaveData[] {
    const waves: WaveData[] = [];

    if (swingPoints.length < 6) return waves;

    // Look for alternating high-low pattern that forms 5 waves
    for (let i = 0; i < swingPoints.length - 5; i++) {
      const potential5Wave = swingPoints.slice(i, i + 6);

      // Check if it forms a valid 5-wave pattern
      if (this.isValid5WavePattern(potential5Wave)) {
        for (let j = 0; j < 5; j++) {
          const start = potential5Wave[j];
          const end = potential5Wave[j + 1];

          waves.push({
            start: start.price,
            end: end.price,
            timestamp: start.timestamp,
            type: this.getWaveType(j + 1),
            strength: this.calculateWaveStrength(start, end, potential5Wave)
          });
        }
        break;
      }
    }

    return waves;
  }

  /**
   * Check if swing points form a valid 5-wave pattern
   */
  private isValid5WavePattern(
    points: Array<{ price: number; type: 'high' | 'low' }>
  ): boolean {
    if (points.length !== 6) return false;

    // Elliott Wave rules:
    // 1. Wave 2 cannot retrace more than 100% of Wave 1
    // 2. Wave 3 cannot be the shortest wave
    // 3. Wave 4 cannot overlap with Wave 1 price territory

    const wave1 = Math.abs(points[1].price - points[0].price);
    const wave2 = Math.abs(points[2].price - points[1].price);
    const wave3 = Math.abs(points[3].price - points[2].price);
    const wave4 = Math.abs(points[4].price - points[3].price);
    const wave5 = Math.abs(points[5].price - points[4].price);

    // Rule 1: Wave 2 retracement
    const wave2Retracement = wave2 / wave1;
    if (wave2Retracement > 1.0) return false;

    // Rule 2: Wave 3 is not the shortest
    if (wave3 < wave1 && wave3 < wave5) return false;

    // Rule 3: Wave 4 overlap check
    const wave1High = Math.max(points[0].price, points[1].price);
    const wave1Low = Math.min(points[0].price, points[1].price);
    const wave4High = Math.max(points[3].price, points[4].price);
    const wave4Low = Math.min(points[3].price, points[4].price);

    if (wave4Low < wave1High && wave4High > wave1Low) return false;

    return true;
  }

  /**
   * Get wave type (impulse or corrective)
   */
  private getWaveType(waveNumber: number): 'impulse' | 'corrective' {
    return [1, 3, 5].includes(waveNumber) ? 'impulse' : 'corrective';
  }

  /**
   * Calculate wave strength
   */
  private calculateWaveStrength(
    start: { price: number },
    end: { price: number },
    context: Array<{ price: number }>
  ): number {
    const waveSize = Math.abs(end.price - start.price);
    const maxPrice = Math.max(...context.map((p) => p.price));
    const minPrice = Math.min(...context.map((p) => p.price));
    const totalRange = maxPrice - minPrice;

    return totalRange > 0 ? waveSize / totalRange : 0;
  }

  /**
   * Calculate wave count confidence
   */
  private calculateWaveCountConfidence(waves: WaveData[]): number {
    if (waves.length < 5) return 0;

    let confidence = 0;

    // Check Fibonacci relationships
    const wave1 = Math.abs(waves[0].end - waves[0].start);
    const wave2 = Math.abs(waves[1].end - waves[1].start);
    const wave3 = Math.abs(waves[2].end - waves[2].start);
    const wave4 = Math.abs(waves[3].end - waves[3].start);
    const wave5 = Math.abs(waves[4].end - waves[4].start);

    // Wave 2 retracement (should be 38.2%, 50%, or 61.8% of Wave 1)
    const wave2Ratio = wave2 / wave1;
    if (this.isNearFibonacci(wave2Ratio, [0.382, 0.5, 0.618])) {
      confidence += 0.2;
    }

    // Wave 3 extension (should be 1.618 or 2.618 of Wave 1)
    const wave3Ratio = wave3 / wave1;
    if (this.isNearFibonacci(wave3Ratio, [1.618, 2.618])) {
      confidence += 0.3;
    }

    // Wave 4 retracement (should be 23.6% or 38.2% of Wave 3)
    const wave4Ratio = wave4 / wave3;
    if (this.isNearFibonacci(wave4Ratio, [0.236, 0.382])) {
      confidence += 0.2;
    }

    // Wave 5 relationship (should equal Wave 1 or be 61.8% of Wave 1-3)
    const wave5Ratio = wave5 / wave1;
    const wave13Distance = Math.abs(waves[2].end - waves[0].start);
    const wave5To13Ratio = wave5 / wave13Distance;
    if (
      this.isNearFibonacci(wave5Ratio, [1.0]) ||
      this.isNearFibonacci(wave5To13Ratio, [0.618])
    ) {
      confidence += 0.3;
    }

    return Math.min(confidence, 1);
  }

  /**
   * Check if a ratio is near Fibonacci levels
   */
  private isNearFibonacci(ratio: number, fibLevels: number[]): boolean {
    const tolerance = this.config.fibonacciTolerance;
    return fibLevels.some(
      (level) => Math.abs(ratio - level) / level < tolerance
    );
  }

  /**
   * Identify current wave
   */
  private identifyCurrentWave(
    waveCount: WaveCount,
    swingPoints: Array<{ price: number; timestamp: number }>
  ): ElliottWaveAnalysis['currentWave'] {
    if (waveCount.confidence < 0.3) {
      return {
        number: 0,
        type: 'impulse',
        degree: 'minute',
        direction: 'up',
        completion: 0
      };
    }

    // Determine which wave we're currently in based on the latest price action
    const latestPoint = swingPoints[swingPoints.length - 1];
    const waves = [
      waveCount.wave1,
      waveCount.wave2,
      waveCount.wave3,
      waveCount.wave4,
      waveCount.wave5
    ];

    let currentWaveNumber = 5; // Default to wave 5
    let completion = 1;

    // Find the most recent wave
    for (let i = waves.length - 1; i >= 0; i--) {
      if (latestPoint.timestamp >= waves[i].timestamp) {
        currentWaveNumber = i + 1;

        // Calculate completion percentage
        const waveStart = waves[i].start;
        const waveEnd = waves[i].end;
        const currentPrice = latestPoint.price;

        if (waveEnd !== waveStart) {
          completion =
            Math.abs(currentPrice - waveStart) / Math.abs(waveEnd - waveStart);
          completion = Math.min(Math.max(completion, 0), 1);
        }
        break;
      }
    }

    return {
      number: currentWaveNumber,
      type: this.getWaveType(currentWaveNumber),
      degree: 'minute', // Simplified - could be enhanced to detect degree
      direction: currentWaveNumber % 2 === 1 ? 'up' : 'down', // Simplified
      completion
    };
  }

  /**
   * Calculate Fibonacci levels
   */
  private calculateFibonacciLevels(waveCount: WaveCount): FibonacciLevel[] {
    const levels: FibonacciLevel[] = [];

    if (waveCount.confidence < 0.3) return levels;

    const wave1Size = Math.abs(waveCount.wave1.end - waveCount.wave1.start);
    const wave3Size = Math.abs(waveCount.wave3.end - waveCount.wave3.start);

    // Retracement levels for Wave 2
    for (const fibLevel of [0.236, 0.382, 0.5, 0.618, 0.786]) {
      levels.push({
        level: fibLevel,
        price: waveCount.wave1.end - wave1Size * fibLevel,
        type: 'retracement',
        significance: this.calculateFibonacciSignificance(
          fibLevel,
          'retracement'
        )
      });
    }

    // Extension levels for Wave 3
    for (const fibLevel of [1.272, 1.618, 2.618]) {
      levels.push({
        level: fibLevel,
        price: waveCount.wave1.start + wave1Size * fibLevel,
        type: 'extension',
        significance: this.calculateFibonacciSignificance(fibLevel, 'extension')
      });
    }

    return levels.sort((a, b) => b.significance - a.significance).slice(0, 10);
  }

  /**
   * Calculate Fibonacci level significance
   */
  private calculateFibonacciSignificance(
    level: number,
    type: 'retracement' | 'extension'
  ): number {
    const keyLevels =
      type === 'retracement' ? [0.382, 0.5, 0.618] : [1.272, 1.618, 2.618];
    return keyLevels.includes(level) ? 1 : 0.5;
  }

  /**
   * Calculate wave projections
   */
  private calculateWaveProjections(
    waveCount: WaveCount,
    timeframe: string
  ): WaveProjection[] {
    const projections: WaveProjection[] = [];

    if (waveCount.confidence < 0.5) return projections;

    const wave1Size = Math.abs(waveCount.wave1.end - waveCount.wave1.start);
    const wave3Size = Math.abs(waveCount.wave3.end - waveCount.wave3.start);

    // Wave 5 projection
    projections.push({
      targetPrice: waveCount.wave4.end + wave1Size, // Wave 5 = Wave 1
      probability: 0.7,
      type: 'wave5',
      timeframe
    });

    projections.push({
      targetPrice: waveCount.wave4.end + wave1Size * 0.618, // Wave 5 = 61.8% of Wave 1
      probability: 0.6,
      type: 'wave5',
      timeframe
    });

    return projections;
  }

  /**
   * Identify wave patterns
   */
  private identifyWavePatterns(
    waveCount: WaveCount
  ): ElliottWaveAnalysis['patterns'] {
    const patterns = {
      impulse: false,
      corrective: false,
      triangle: false,
      flat: false,
      zigzag: false
    };

    if (waveCount.confidence > 0.5) {
      patterns.impulse = true; // Simplified - we're looking for 5-wave impulse patterns
    }

    return patterns;
  }

  private calculateSignal(
    analysis: ElliottWaveAnalysis,
    currentPrice: number
  ): 'BUY' | 'SELL' | 'NEUTRAL' {
    if (analysis.waveCount.confidence < this.config.waveCountAccuracy) {
      return 'NEUTRAL';
    }

    const currentWave = analysis.currentWave;

    // Wave 3 and 5 are typically bullish in an uptrend
    if (currentWave.number === 3 || currentWave.number === 5) {
      if (currentWave.direction === 'up' && currentWave.completion < 0.8) {
        return 'BUY';
      } else if (
        currentWave.direction === 'down' &&
        currentWave.completion < 0.8
      ) {
        return 'SELL';
      }
    }

    // Wave 2 and 4 are corrective - look for reversal opportunities
    if (currentWave.number === 2 || currentWave.number === 4) {
      if (currentWave.completion > 0.7) {
        return currentWave.direction === 'down' ? 'BUY' : 'SELL';
      }
    }

    // Check Fibonacci level proximity
    const nearFibLevel = analysis.fibonacciLevels.find(
      (level) => Math.abs(currentPrice - level.price) / currentPrice < 0.001
    );

    if (nearFibLevel && nearFibLevel.significance > 0.8) {
      return nearFibLevel.type === 'retracement' ? 'BUY' : 'SELL';
    }

    return 'NEUTRAL';
  }

  private calculateConfidence(
    analysis: ElliottWaveAnalysis,
    signal: 'BUY' | 'SELL' | 'NEUTRAL'
  ): number {
    if (signal === 'NEUTRAL') return 0;

    let confidence = 0;

    // Base confidence from wave count accuracy
    confidence += analysis.waveCount.confidence * 50;

    // Current wave position confidence
    if (analysis.currentWave.number === 3) {
      confidence += 30; // Wave 3 is usually the strongest
    } else if (analysis.currentWave.number === 5) {
      confidence += 20;
    } else if (analysis.currentWave.number === 1) {
      confidence += 15;
    }

    // Fibonacci level proximity
    const significantFibLevels = analysis.fibonacciLevels.filter(
      (level) => level.significance > 0.8
    );
    confidence += Math.min(significantFibLevels.length * 5, 20);

    return Math.min(confidence, 100);
  }

  private calculateStrength(analysis: ElliottWaveAnalysis): number {
    let strength = 0;

    // Wave count confidence
    strength += analysis.waveCount.confidence * 0.4;

    // Current wave strength
    if (analysis.currentWave.number === 3) {
      strength += 0.4; // Wave 3 is typically the strongest
    } else if (analysis.currentWave.number === 5) {
      strength += 0.3;
    } else if (analysis.currentWave.number === 1) {
      strength += 0.2;
    }

    // Pattern confirmation
    if (analysis.patterns.impulse) {
      strength += 0.2;
    }

    return Math.min(strength, 1);
  }

  private generateReasoning(
    analysis: ElliottWaveAnalysis,
    signal: 'BUY' | 'SELL' | 'NEUTRAL'
  ): string {
    const reasons: string[] = [];

    if (analysis.waveCount.confidence > 0.5) {
      reasons.push(
        `Wave count confidence: ${(analysis.waveCount.confidence * 100).toFixed(
          1
        )}%`
      );
    }

    reasons.push(
      `Current wave: ${analysis.currentWave.number} (${analysis.currentWave.type})`
    );
    reasons.push(
      `Wave completion: ${(analysis.currentWave.completion * 100).toFixed(1)}%`
    );

    if (analysis.fibonacciLevels.length > 0) {
      const significantLevels = analysis.fibonacciLevels.filter(
        (level) => level.significance > 0.8
      );
      reasons.push(`${significantLevels.length} significant Fibonacci levels`);
    }

    if (analysis.projections.length > 0) {
      const highProbProjections = analysis.projections.filter(
        (proj) => proj.probability > 0.6
      );
      reasons.push(
        `${highProbProjections.length} high-probability projections`
      );
    }

    return reasons.join('; ') || 'Elliott Wave analysis inconclusive';
  }

  private storeWaveHistory(symbol: string, waveCount: WaveCount): void {
    if (!this.waveHistory.has(symbol)) {
      this.waveHistory.set(symbol, []);
    }

    const history = this.waveHistory.get(symbol)!;
    const waves = [
      waveCount.wave1,
      waveCount.wave2,
      waveCount.wave3,
      waveCount.wave4,
      waveCount.wave5
    ];
    history.push(...waves);

    // Keep only last 50 waves
    if (history.length > 50) {
      history.splice(0, history.length - 50);
    }
  }

  private getEmptyWaveCount(): WaveCount {
    const emptyWave: WaveData = {
      start: 0,
      end: 0,
      timestamp: 0,
      type: 'impulse',
      strength: 0
    };

    return {
      wave1: emptyWave,
      wave2: emptyWave,
      wave3: emptyWave,
      wave4: emptyWave,
      wave5: emptyWave,
      confidence: 0
    };
  }

  private getEmptyAnalysis(): ElliottWaveAnalysis {
    return {
      currentWave: {
        number: 0,
        type: 'impulse',
        degree: 'minute',
        direction: 'up',
        completion: 0
      },
      waveCount: this.getEmptyWaveCount(),
      fibonacciLevels: [],
      projections: [],
      patterns: {
        impulse: false,
        corrective: false,
        triangle: false,
        flat: false,
        zigzag: false
      }
    };
  }

  private getEmptySignal(timeframe: string): MethodologySignal {
    return {
      methodology: 'ElliottWave',
      confidence: 0,
      signal: 'NEUTRAL',
      strength: 0,
      reasoning: 'Elliott Wave analysis disabled or insufficient data',
      timeframe,
      metadata: {}
    };
  }
}

// Export singleton instance
export const elliottWaveService = new ElliottWaveService();
