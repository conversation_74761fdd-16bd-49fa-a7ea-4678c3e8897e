import { EventEmitter } from 'events';
import { config } from '../../config';
import {
  CandlestickData,
  CharacterChange,
  InstitutionalLevel,
  MethodologySignal,
  SMCAnalysis,
  StructureBreak,
  TechnicalIndicators
} from '../../types';
import { logger } from '../../utils/logger';

export interface SMCConfig {
  enabled: boolean;
  weight: number;
  minConfidence: number;
  structureBreakThreshold: number;
  characterChangeThreshold: number;
}

export class SMCService extends EventEmitter {
  private config: SMCConfig;
  private structureHistory: Map<string, StructureBreak[]> = new Map();
  private characterHistory: Map<string, CharacterChange[]> = new Map();
  private institutionalLevelHistory: Map<string, InstitutionalLevel[]> = new Map();

  constructor() {
    super();
    this.config = config.methodologies.smc;
  }

  /**
   * Analyze SMC concepts for a given symbol and timeframe
   */
  public async analyze(
    symbol: string,
    candles: CandlestickData[],
    timeframe: string,
    indicators?: TechnicalIndicators
  ): Promise<SMCAnalysis> {
    try {
      if (!this.config.enabled || candles.length < 30) {
        return this.getEmptyAnalysis();
      }

      const analysis: SMCAnalysis = {
        orderFlow: await this.analyzeOrderFlow(candles),
        structureBreaks: await this.detectStructureBreaks(candles, timeframe),
        characterChanges: await this.detectCharacterChanges(candles, timeframe),
        smartMoneyFootprint: await this.analyzeSmartMoneyFootprint(candles),
        institutionalLevels: await this.identifyInstitutionalLevels(candles, timeframe)
      };

      // Store analysis for future reference
      this.storeAnalysisHistory(symbol, analysis);

      logger.trading('debug', `SMC analysis completed for ${symbol}`, {
        timeframe,
        orderFlow: analysis.orderFlow.direction,
        structureBreaks: analysis.structureBreaks.bullish.length + analysis.structureBreaks.bearish.length,
        characterChanges: analysis.characterChanges.length,
        institutionalLevels: analysis.institutionalLevels.length
      });

      return analysis;
    } catch (error) {
      logger.trading('error', `SMC analysis failed for ${symbol}`, { error });
      return this.getEmptyAnalysis();
    }
  }

  /**
   * Generate trading signal based on SMC analysis
   */
  public generateSignal(
    symbol: string,
    analysis: SMCAnalysis,
    currentPrice: number,
    timeframe: string
  ): MethodologySignal {
    try {
      const signal = this.calculateSignal(analysis, currentPrice);
      const confidence = this.calculateConfidence(analysis, signal);
      const reasoning = this.generateReasoning(analysis, signal);

      return {
        methodology: 'SMC',
        confidence,
        signal,
        strength: this.calculateStrength(analysis),
        reasoning,
        timeframe,
        metadata: {
          orderFlow: analysis.orderFlow,
          structureBreaks: analysis.structureBreaks,
          characterChanges: analysis.characterChanges,
          smartMoneyFootprint: analysis.smartMoneyFootprint,
          institutionalLevels: analysis.institutionalLevels
        }
      };
    } catch (error) {
      logger.trading('error', `SMC signal generation failed for ${symbol}`, { error });
      return this.getEmptySignal(timeframe);
    }
  }

  /**
   * Analyze institutional order flow
   */
  private async analyzeOrderFlow(candles: CandlestickData[]): Promise<SMCAnalysis['orderFlow']> {
    const recentCandles = candles.slice(-20);
    let bullishFlow = 0;
    let bearishFlow = 0;
    let totalVolume = 0;

    for (const candle of recentCandles) {
      const volume = candle.volume || 1;
      const bodySize = Math.abs(candle.close - candle.open);
      const range = candle.high - candle.low;
      const bodyRatio = bodySize / range;

      totalVolume += volume;

      // Strong bullish candle with high volume
      if (candle.close > candle.open && bodyRatio > 0.7) {
        bullishFlow += volume * bodyRatio;
      }
      // Strong bearish candle with high volume
      else if (candle.close < candle.open && bodyRatio > 0.7) {
        bearishFlow += volume * bodyRatio;
      }
    }

    const netFlow = bullishFlow - bearishFlow;
    const flowStrength = Math.abs(netFlow) / totalVolume;
    const flowConfidence = Math.min(flowStrength * 2, 1);

    let direction: 'bullish' | 'bearish' | 'neutral' = 'neutral';
    if (netFlow > 0 && flowStrength > 0.3) {
      direction = 'bullish';
    } else if (netFlow < 0 && flowStrength > 0.3) {
      direction = 'bearish';
    }

    return {
      direction,
      strength: flowStrength,
      confidence: flowConfidence
    };
  }

  /**
   * Detect structure breaks (BOS)
   */
  private async detectStructureBreaks(candles: CandlestickData[], timeframe: string): Promise<SMCAnalysis['structureBreaks']> {
    const bullish: StructureBreak[] = [];
    const bearish: StructureBreak[] = [];

    // Identify swing points
    const swingHighs = this.identifySwingHighs(candles);
    const swingLows = this.identifySwingLows(candles);

    // Detect bullish structure breaks (break above previous swing high)
    for (let i = 1; i < swingHighs.length; i++) {
      const currentHigh = swingHighs[i];
      const previousHigh = swingHighs[i - 1];

      if (currentHigh.price > previousHigh.price) {
        const strength = this.calculateStructureBreakStrength(currentHigh, previousHigh, candles);
        
        if (strength > this.config.structureBreakThreshold) {
          bullish.push({
            price: currentHigh.price,
            timestamp: currentHigh.timestamp,
            type: 'bullish',
            strength,
            confirmed: this.confirmStructureBreak(currentHigh, candles, 'bullish'),
            timeframe
          });
        }
      }
    }

    // Detect bearish structure breaks (break below previous swing low)
    for (let i = 1; i < swingLows.length; i++) {
      const currentLow = swingLows[i];
      const previousLow = swingLows[i - 1];

      if (currentLow.price < previousLow.price) {
        const strength = this.calculateStructureBreakStrength(currentLow, previousLow, candles);
        
        if (strength > this.config.structureBreakThreshold) {
          bearish.push({
            price: currentLow.price,
            timestamp: currentLow.timestamp,
            type: 'bearish',
            strength,
            confirmed: this.confirmStructureBreak(currentLow, candles, 'bearish'),
            timeframe
          });
        }
      }
    }

    return {
      bullish: bullish.slice(-10),
      bearish: bearish.slice(-10)
    };
  }

  /**
   * Detect character changes (CHoCH)
   */
  private async detectCharacterChanges(candles: CandlestickData[], timeframe: string): Promise<CharacterChange[]> {
    const characterChanges: CharacterChange[] = [];
    const swingPoints = [...this.identifySwingHighs(candles), ...this.identifySwingLows(candles)]
      .sort((a, b) => a.timestamp - b.timestamp);

    let currentCharacter: 'bullish' | 'bearish' | 'neutral' = 'neutral';

    for (let i = 2; i < swingPoints.length; i++) {
      const prev2 = swingPoints[i - 2];
      const prev1 = swingPoints[i - 1];
      const current = swingPoints[i];

      // Determine new character
      let newCharacter: 'bullish' | 'bearish' | 'neutral' = 'neutral';

      // Bullish character: higher highs and higher lows
      if (current.type === 'high' && prev1.type === 'low' && 
          current.price > prev2.price && prev1.price > (swingPoints[i - 3]?.price || 0)) {
        newCharacter = 'bullish';
      }
      // Bearish character: lower highs and lower lows
      else if (current.type === 'low' && prev1.type === 'high' && 
               current.price < prev2.price && prev1.price < (swingPoints[i - 3]?.price || Infinity)) {
        newCharacter = 'bearish';
      }

      // Detect character change
      if (newCharacter !== 'neutral' && newCharacter !== currentCharacter && currentCharacter !== 'neutral') {
        const strength = this.calculateCharacterChangeStrength(current, prev1, prev2);
        
        if (strength > this.config.characterChangeThreshold) {
          characterChanges.push({
            price: current.price,
            timestamp: current.timestamp,
            from: currentCharacter,
            to: newCharacter,
            strength,
            timeframe
          });
        }
      }

      if (newCharacter !== 'neutral') {
        currentCharacter = newCharacter;
      }
    }

    return characterChanges.slice(-5);
  }

  /**
   * Analyze smart money footprint
   */
  private async analyzeSmartMoneyFootprint(candles: CandlestickData[]): Promise<SMCAnalysis['smartMoneyFootprint']> {
    const recentCandles = candles.slice(-50);
    
    // Accumulation detection
    const accumulation = this.detectAccumulation(recentCandles);
    
    // Distribution detection
    const distribution = this.detectDistribution(recentCandles);
    
    // Manipulation detection
    const manipulation = this.detectManipulation(recentCandles);

    return {
      accumulation,
      distribution,
      manipulation
    };
  }

  /**
   * Identify institutional levels
   */
  private async identifyInstitutionalLevels(candles: CandlestickData[], timeframe: string): Promise<InstitutionalLevel[]> {
    const levels: InstitutionalLevel[] = [];
    const recentCandles = candles.slice(-100);

    // Find levels with multiple reactions
    const priceReactions = new Map<number, { count: number, strength: number, type: 'support' | 'resistance' }>();
    const tolerance = 0.0002; // 2 pips tolerance

    for (let i = 5; i < recentCandles.length - 5; i++) {
      const candle = recentCandles[i];
      const before = recentCandles.slice(i - 5, i);
      const after = recentCandles.slice(i + 1, i + 6);

      // Check for support level
      if (this.isSupport(candle, before, after)) {
        const roundedPrice = Math.round(candle.low / tolerance) * tolerance;
        const existing = priceReactions.get(roundedPrice);
        const strength = this.calculateLevelStrength(candle, before, after);
        
        if (existing) {
          existing.count++;
          existing.strength = Math.max(existing.strength, strength);
        } else {
          priceReactions.set(roundedPrice, { count: 1, strength, type: 'support' });
        }
      }

      // Check for resistance level
      if (this.isResistance(candle, before, after)) {
        const roundedPrice = Math.round(candle.high / tolerance) * tolerance;
        const existing = priceReactions.get(roundedPrice);
        const strength = this.calculateLevelStrength(candle, before, after);
        
        if (existing) {
          existing.count++;
          existing.strength = Math.max(existing.strength, strength);
        } else {
          priceReactions.set(roundedPrice, { count: 1, strength, type: 'resistance' });
        }
      }
    }

    // Filter significant levels
    for (const [price, data] of priceReactions.entries()) {
      if (data.count >= 3 && data.strength > 0.5) {
        levels.push({
          price,
          type: data.type,
          strength: data.strength,
          tested: data.count,
          timeframe
        });
      }
    }

    return levels.sort((a, b) => b.strength - a.strength).slice(0, 15);
  }

  // Helper methods
  private identifySwingHighs(candles: CandlestickData[]): Array<{price: number, timestamp: number, type: 'high'}> {
    const swingHighs: Array<{price: number, timestamp: number, type: 'high'}> = [];
    
    for (let i = 5; i < candles.length - 5; i++) {
      const current = candles[i];
      const before = candles.slice(i - 5, i);
      const after = candles.slice(i + 1, i + 6);
      
      if (before.every(c => c.high < current.high) && after.every(c => c.high < current.high)) {
        swingHighs.push({
          price: current.high,
          timestamp: current.timestamp,
          type: 'high'
        });
      }
    }
    
    return swingHighs;
  }

  private identifySwingLows(candles: CandlestickData[]): Array<{price: number, timestamp: number, type: 'low'}> {
    const swingLows: Array<{price: number, timestamp: number, type: 'low'}> = [];
    
    for (let i = 5; i < candles.length - 5; i++) {
      const current = candles[i];
      const before = candles.slice(i - 5, i);
      const after = candles.slice(i + 1, i + 6);
      
      if (before.every(c => c.low > current.low) && after.every(c => c.low > current.low)) {
        swingLows.push({
          price: current.low,
          timestamp: current.timestamp,
          type: 'low'
        });
      }
    }
    
    return swingLows;
  }

  private calculateStructureBreakStrength(
    current: {price: number, timestamp: number}, 
    previous: {price: number, timestamp: number}, 
    candles: CandlestickData[]
  ): number {
    const priceMove = Math.abs(current.price - previous.price) / previous.price;
    const timeGap = current.timestamp - previous.timestamp;
    const momentum = priceMove / (timeGap / 3600000); // Price move per hour
    
    return Math.min(momentum * 1000, 1);
  }

  private confirmStructureBreak(
    point: {price: number, timestamp: number}, 
    candles: CandlestickData[], 
    type: 'bullish' | 'bearish'
  ): boolean {
    const futureCandles = candles.filter(c => c.timestamp > point.timestamp).slice(0, 10);
    let confirmationCount = 0;

    for (const candle of futureCandles) {
      if (type === 'bullish' && candle.close > point.price) {
        confirmationCount++;
      } else if (type === 'bearish' && candle.close < point.price) {
        confirmationCount++;
      }
    }

    return confirmationCount >= 3;
  }

  private calculateCharacterChangeStrength(
    current: {price: number}, 
    prev1: {price: number}, 
    prev2: {price: number}
  ): number {
    const move1 = Math.abs(prev1.price - prev2.price) / prev2.price;
    const move2 = Math.abs(current.price - prev1.price) / prev1.price;
    const consistency = Math.min(move1, move2) / Math.max(move1, move2);
    
    return consistency * Math.max(move1, move2) * 100;
  }

  private detectAccumulation(candles: CandlestickData[]): boolean {
    // Look for sideways movement with increasing volume
    const priceRange = Math.max(...candles.map(c => c.high)) - Math.min(...candles.map(c => c.low));
    const avgPrice = candles.reduce((sum, c) => sum + c.close, 0) / candles.length;
    const rangePercent = priceRange / avgPrice;
    
    const volumes = candles.map(c => c.volume || 1);
    const avgVolume = volumes.reduce((sum, v) => sum + v, 0) / volumes.length;
    const recentVolume = volumes.slice(-10).reduce((sum, v) => sum + v, 0) / 10;
    
    return rangePercent < 0.02 && recentVolume > avgVolume * 1.2;
  }

  private detectDistribution(candles: CandlestickData[]): boolean {
    // Look for sideways movement at highs with high volume
    const highs = candles.map(c => c.high);
    const recentHighs = highs.slice(-20);
    const maxHigh = Math.max(...highs);
    const recentMaxHigh = Math.max(...recentHighs);
    
    const volumes = candles.map(c => c.volume || 1);
    const avgVolume = volumes.reduce((sum, v) => sum + v, 0) / volumes.length;
    const recentVolume = volumes.slice(-10).reduce((sum, v) => sum + v, 0) / 10;
    
    return recentMaxHigh >= maxHigh * 0.98 && recentVolume > avgVolume * 1.3;
  }

  private detectManipulation(candles: CandlestickData[]): boolean {
    // Look for sudden spikes followed by reversals
    for (let i = 1; i < candles.length - 1; i++) {
      const prev = candles[i - 1];
      const current = candles[i];
      const next = candles[i + 1];
      
      const upSpike = current.high > prev.high * 1.005 && next.close < current.open;
      const downSpike = current.low < prev.low * 0.995 && next.close > current.open;
      
      if (upSpike || downSpike) {
        return true;
      }
    }
    
    return false;
  }

  private isSupport(candle: CandlestickData, before: CandlestickData[], after: CandlestickData[]): boolean {
    return before.every(c => c.low > candle.low) && after.every(c => c.low > candle.low);
  }

  private isResistance(candle: CandlestickData, before: CandlestickData[], after: CandlestickData[]): boolean {
    return before.every(c => c.high < candle.high) && after.every(c => c.high < candle.high);
  }

  private calculateLevelStrength(
    candle: CandlestickData, 
    before: CandlestickData[], 
    after: CandlestickData[]
  ): number {
    const volume = candle.volume || 1;
    const avgVolume = [...before, ...after].reduce((sum, c) => sum + (c.volume || 1), 0) / (before.length + after.length);
    const priceSignificance = Math.abs(candle.high - candle.low) / candle.close;
    
    return Math.min((volume / avgVolume) * priceSignificance * 5, 1);
  }

  private calculateSignal(analysis: SMCAnalysis, currentPrice: number): 'BUY' | 'SELL' | 'NEUTRAL' {
    let bullishScore = 0;
    let bearishScore = 0;

    // Order flow analysis
    if (analysis.orderFlow.direction === 'bullish') {
      bullishScore += analysis.orderFlow.strength * analysis.orderFlow.confidence * 0.4;
    } else if (analysis.orderFlow.direction === 'bearish') {
      bearishScore += analysis.orderFlow.strength * analysis.orderFlow.confidence * 0.4;
    }

    // Structure breaks
    const recentBullishBreaks = analysis.structureBreaks.bullish.filter(sb => sb.confirmed);
    const recentBearishBreaks = analysis.structureBreaks.bearish.filter(sb => sb.confirmed);
    
    if (recentBullishBreaks.length > recentBearishBreaks.length) {
      bullishScore += 0.3;
    } else if (recentBearishBreaks.length > recentBullishBreaks.length) {
      bearishScore += 0.3;
    }

    // Character changes
    const recentCharacterChange = analysis.characterChanges[analysis.characterChanges.length - 1];
    if (recentCharacterChange) {
      if (recentCharacterChange.to === 'bullish') {
        bullishScore += recentCharacterChange.strength * 0.2;
      } else if (recentCharacterChange.to === 'bearish') {
        bearishScore += recentCharacterChange.strength * 0.2;
      }
    }

    // Smart money footprint
    if (analysis.smartMoneyFootprint.accumulation) {
      bullishScore += 0.1;
    }
    if (analysis.smartMoneyFootprint.distribution) {
      bearishScore += 0.1;
    }

    if (bullishScore > bearishScore && bullishScore > 0.6) {
      return 'BUY';
    } else if (bearishScore > bullishScore && bearishScore > 0.6) {
      return 'SELL';
    }
    return 'NEUTRAL';
  }

  private calculateConfidence(analysis: SMCAnalysis, signal: 'BUY' | 'SELL' | 'NEUTRAL'): number {
    if (signal === 'NEUTRAL') return 0;

    let confidence = 0;

    // Order flow confidence
    confidence += analysis.orderFlow.confidence * 40;

    // Structure break confirmation
    const relevantBreaks = signal === 'BUY' ? analysis.structureBreaks.bullish : analysis.structureBreaks.bearish;
    const confirmedBreaks = relevantBreaks.filter(sb => sb.confirmed);
    confidence += Math.min(confirmedBreaks.length * 15, 30);

    // Character change strength
    const recentCharacterChange = analysis.characterChanges[analysis.characterChanges.length - 1];
    if (recentCharacterChange && 
        ((signal === 'BUY' && recentCharacterChange.to === 'bullish') ||
         (signal === 'SELL' && recentCharacterChange.to === 'bearish'))) {
      confidence += recentCharacterChange.strength * 20;
    }

    // Institutional levels
    confidence += Math.min(analysis.institutionalLevels.length * 2, 10);

    return Math.min(confidence, 100);
  }

  private calculateStrength(analysis: SMCAnalysis): number {
    let strength = 0;

    // Order flow strength
    strength += analysis.orderFlow.strength * analysis.orderFlow.confidence * 0.4;

    // Structure break strength
    const avgBreakStrength = [...analysis.structureBreaks.bullish, ...analysis.structureBreaks.bearish]
      .reduce((sum, sb) => sum + sb.strength, 0) / 
      (analysis.structureBreaks.bullish.length + analysis.structureBreaks.bearish.length || 1);
    strength += avgBreakStrength * 0.3;

    // Character change strength
    const avgCharacterStrength = analysis.characterChanges
      .reduce((sum, cc) => sum + cc.strength, 0) / (analysis.characterChanges.length || 1);
    strength += avgCharacterStrength * 0.2;

    // Smart money footprint
    const footprintScore = (
      (analysis.smartMoneyFootprint.accumulation ? 1 : 0) +
      (analysis.smartMoneyFootprint.distribution ? 1 : 0) +
      (analysis.smartMoneyFootprint.manipulation ? 1 : 0)
    ) / 3;
    strength += footprintScore * 0.1;

    return Math.min(strength, 1);
  }

  private generateReasoning(analysis: SMCAnalysis, signal: 'BUY' | 'SELL' | 'NEUTRAL'): string {
    const reasons: string[] = [];

    if (analysis.orderFlow.direction !== 'neutral') {
      reasons.push(`Order flow: ${analysis.orderFlow.direction} (${(analysis.orderFlow.confidence * 100).toFixed(1)}% confidence)`);
    }

    const totalBreaks = analysis.structureBreaks.bullish.length + analysis.structureBreaks.bearish.length;
    if (totalBreaks > 0) {
      reasons.push(`${totalBreaks} structure breaks detected`);
    }

    if (analysis.characterChanges.length > 0) {
      const recent = analysis.characterChanges[analysis.characterChanges.length - 1];
      reasons.push(`Character change: ${recent.from} → ${recent.to}`);
    }

    const footprintFeatures = [];
    if (analysis.smartMoneyFootprint.accumulation) footprintFeatures.push('accumulation');
    if (analysis.smartMoneyFootprint.distribution) footprintFeatures.push('distribution');
    if (analysis.smartMoneyFootprint.manipulation) footprintFeatures.push('manipulation');
    
    if (footprintFeatures.length > 0) {
      reasons.push(`Smart money: ${footprintFeatures.join(', ')}`);
    }

    return reasons.join('; ') || 'SMC analysis inconclusive';
  }

  private storeAnalysisHistory(symbol: string, analysis: SMCAnalysis): void {
    // Store structure breaks
    if (!this.structureHistory.has(symbol)) {
      this.structureHistory.set(symbol, []);
    }
    const structureHistory = this.structureHistory.get(symbol)!;
    structureHistory.push(...analysis.structureBreaks.bullish, ...analysis.structureBreaks.bearish);
    if (structureHistory.length > 100) {
      structureHistory.splice(0, structureHistory.length - 100);
    }

    // Store character changes
    if (!this.characterHistory.has(symbol)) {
      this.characterHistory.set(symbol, []);
    }
    const characterHistory = this.characterHistory.get(symbol)!;
    characterHistory.push(...analysis.characterChanges);
    if (characterHistory.length > 50) {
      characterHistory.splice(0, characterHistory.length - 50);
    }

    // Store institutional levels
    if (!this.institutionalLevelHistory.has(symbol)) {
      this.institutionalLevelHistory.set(symbol, []);
    }
    const levelHistory = this.institutionalLevelHistory.get(symbol)!;
    levelHistory.push(...analysis.institutionalLevels);
    if (levelHistory.length > 50) {
      levelHistory.splice(0, levelHistory.length - 50);
    }
  }

  private getEmptyAnalysis(): SMCAnalysis {
    return {
      orderFlow: {
        direction: 'neutral',
        strength: 0,
        confidence: 0
      },
      structureBreaks: {
        bullish: [],
        bearish: []
      },
      characterChanges: [],
      smartMoneyFootprint: {
        accumulation: false,
        distribution: false,
        manipulation: false
      },
      institutionalLevels: []
    };
  }

  private getEmptySignal(timeframe: string): MethodologySignal {
    return {
      methodology: 'SMC',
      confidence: 0,
      signal: 'NEUTRAL',
      strength: 0,
      reasoning: 'SMC analysis disabled or insufficient data',
      timeframe,
      metadata: {}
    };
  }
}

// Export singleton instance
export const smcService = new SMCService();
