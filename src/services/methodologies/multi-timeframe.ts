import { EventEmitter } from 'events';
import { config } from '../../config';
import {
  CandlestickData,
  MethodologySignal,
  MultiTimeframeConfig,
  TechnicalIndicators,
  TimeframeConfig
} from '../../types';
import { logger } from '../../utils/logger';
import { elliottWaveService } from './elliott-wave';
import { ictService } from './ict';
import { priceActionService } from './price-action';
import { smcService } from './smc';
import { wyckoffService } from './wyckoff';

export interface MultiTimeframeAnalysis {
  timeframe: string;
  signals: MethodologySignal[];
  correlation: number;
  bias: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  strength: number;
  confidence: number;
}

export interface TimeframeCorrelation {
  timeframe1: string;
  timeframe2: string;
  correlation: number;
  agreement: boolean;
}

export interface MultiTimeframeResult {
  primaryTimeframe: string;
  analyses: MultiTimeframeAnalysis[];
  correlations: TimeframeCorrelation[];
  overallBias: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  confluenceScore: number;
  recommendedSignal: MethodologySignal;
}

export class MultiTimeframeEngine extends EventEmitter {
  private config: MultiTimeframeConfig;
  private dataCache: Map<string, Map<string, CandlestickData[]>> = new Map();
  private analysisCache: Map<string, MultiTimeframeResult> = new Map();

  constructor() {
    super();
    this.config = config.multiTimeframe;
  }

  /**
   * Perform comprehensive multi-timeframe analysis
   */
  public async analyze(
    symbol: string,
    timeframeData: Map<string, CandlestickData[]>,
    indicators?: Map<string, TechnicalIndicators>
  ): Promise<MultiTimeframeResult> {
    try {
      if (!this.config.enabled) {
        return this.getEmptyResult(symbol);
      }

      // Cache the data
      this.dataCache.set(symbol, timeframeData);

      const analyses: MultiTimeframeAnalysis[] = [];
      const enabledTimeframes = this.config.timeframes.filter(tf => tf.enabled);

      // Analyze each timeframe
      for (const timeframeConfig of enabledTimeframes) {
        const candles = timeframeData.get(timeframeConfig.interval);
        if (!candles || candles.length < 20) continue;

        const timeframeIndicators = indicators?.get(timeframeConfig.interval);
        const analysis = await this.analyzeTimeframe(
          symbol,
          timeframeConfig,
          candles,
          timeframeIndicators
        );
        
        if (analysis) {
          analyses.push(analysis);
        }
      }

      // Calculate correlations between timeframes
      const correlations = this.calculateTimeframeCorrelations(analyses);

      // Determine overall bias and confluence
      const overallBias = this.calculateOverallBias(analyses);
      const confluenceScore = this.calculateConfluenceScore(analyses, correlations);

      // Generate recommended signal
      const recommendedSignal = this.generateRecommendedSignal(
        symbol,
        analyses,
        overallBias,
        confluenceScore
      );

      const result: MultiTimeframeResult = {
        primaryTimeframe: this.getPrimaryTimeframe(enabledTimeframes),
        analyses,
        correlations,
        overallBias,
        confluenceScore,
        recommendedSignal
      };

      // Cache the result
      this.analysisCache.set(symbol, result);

      logger.trading('debug', `Multi-timeframe analysis completed for ${symbol}`, {
        timeframes: analyses.length,
        overallBias,
        confluenceScore,
        recommendedSignal: recommendedSignal.signal
      });

      return result;
    } catch (error) {
      logger.trading('error', `Multi-timeframe analysis failed for ${symbol}`, { error });
      return this.getEmptyResult(symbol);
    }
  }

  /**
   * Analyze a single timeframe using all methodologies
   */
  private async analyzeTimeframe(
    symbol: string,
    timeframeConfig: TimeframeConfig,
    candles: CandlestickData[],
    indicators?: TechnicalIndicators
  ): Promise<MultiTimeframeAnalysis | null> {
    try {
      const signals: MethodologySignal[] = [];
      const timeframe = timeframeConfig.name;

      // Price Action Analysis
      if (config.methodologies.priceAction.enabled) {
        const priceActionAnalysis = await priceActionService.analyze(
          symbol,
          candles,
          timeframe,
          indicators
        );
        const priceActionSignal = priceActionService.generateSignal(
          symbol,
          priceActionAnalysis,
          candles[candles.length - 1].close,
          timeframe
        );
        signals.push(priceActionSignal);
      }

      // ICT Analysis
      if (config.methodologies.ict.enabled) {
        const ictAnalysis = await ictService.analyze(symbol, candles, timeframe, indicators);
        const ictSignal = ictService.generateSignal(
          symbol,
          ictAnalysis,
          candles[candles.length - 1].close,
          timeframe
        );
        signals.push(ictSignal);
      }

      // SMC Analysis
      if (config.methodologies.smc.enabled) {
        const smcAnalysis = await smcService.analyze(symbol, candles, timeframe, indicators);
        const smcSignal = smcService.generateSignal(
          symbol,
          smcAnalysis,
          candles[candles.length - 1].close,
          timeframe
        );
        signals.push(smcSignal);
      }

      // Elliott Wave Analysis
      if (config.methodologies.elliottWave.enabled) {
        const elliottWaveAnalysis = await elliottWaveService.analyze(
          symbol,
          candles,
          timeframe,
          indicators
        );
        const elliottWaveSignal = elliottWaveService.generateSignal(
          symbol,
          elliottWaveAnalysis,
          candles[candles.length - 1].close,
          timeframe
        );
        signals.push(elliottWaveSignal);
      }

      // Wyckoff Analysis
      if (config.methodologies.wyckoff.enabled) {
        const wyckoffAnalysis = await wyckoffService.analyze(
          symbol,
          candles,
          timeframe,
          indicators
        );
        const wyckoffSignal = wyckoffService.generateSignal(
          symbol,
          wyckoffAnalysis,
          candles[candles.length - 1].close,
          timeframe
        );
        signals.push(wyckoffSignal);
      }

      // Calculate timeframe bias and metrics
      const bias = this.calculateTimeframeBias(signals);
      const strength = this.calculateTimeframeStrength(signals);
      const confidence = this.calculateTimeframeConfidence(signals);
      const correlation = this.calculateTimeframeCorrelation(signals);

      return {
        timeframe,
        signals,
        correlation,
        bias,
        strength,
        confidence
      };
    } catch (error) {
      logger.trading('error', `Timeframe analysis failed for ${symbol} ${timeframeConfig.name}`, { error });
      return null;
    }
  }

  /**
   * Calculate bias for a specific timeframe
   */
  private calculateTimeframeBias(signals: MethodologySignal[]): 'BULLISH' | 'BEARISH' | 'NEUTRAL' {
    let bullishScore = 0;
    let bearishScore = 0;
    let totalWeight = 0;

    for (const signal of signals) {
      const methodologyConfig = this.getMethodologyConfig(signal.methodology);
      const weight = methodologyConfig?.weight || 0.2;
      const confidence = signal.confidence / 100;

      totalWeight += weight;

      if (signal.signal === 'BUY') {
        bullishScore += weight * confidence;
      } else if (signal.signal === 'SELL') {
        bearishScore += weight * confidence;
      }
    }

    if (totalWeight === 0) return 'NEUTRAL';

    const normalizedBullish = bullishScore / totalWeight;
    const normalizedBearish = bearishScore / totalWeight;

    if (normalizedBullish > normalizedBearish && normalizedBullish > 0.6) {
      return 'BULLISH';
    } else if (normalizedBearish > normalizedBullish && normalizedBearish > 0.6) {
      return 'BEARISH';
    }

    return 'NEUTRAL';
  }

  /**
   * Calculate strength for a specific timeframe
   */
  private calculateTimeframeStrength(signals: MethodologySignal[]): number {
    if (signals.length === 0) return 0;

    const avgStrength = signals.reduce((sum, signal) => sum + signal.strength, 0) / signals.length;
    const avgConfidence = signals.reduce((sum, signal) => sum + signal.confidence, 0) / signals.length;

    return (avgStrength + avgConfidence / 100) / 2;
  }

  /**
   * Calculate confidence for a specific timeframe
   */
  private calculateTimeframeConfidence(signals: MethodologySignal[]): number {
    if (signals.length === 0) return 0;

    // Calculate agreement between methodologies
    const buySignals = signals.filter(s => s.signal === 'BUY').length;
    const sellSignals = signals.filter(s => s.signal === 'SELL').length;
    const neutralSignals = signals.filter(s => s.signal === 'NEUTRAL').length;

    const totalSignals = signals.length;
    const maxAgreement = Math.max(buySignals, sellSignals, neutralSignals);
    const agreementRatio = maxAgreement / totalSignals;

    // Weight by average confidence
    const avgConfidence = signals.reduce((sum, signal) => sum + signal.confidence, 0) / signals.length;

    return (agreementRatio * 0.6 + (avgConfidence / 100) * 0.4) * 100;
  }

  /**
   * Calculate correlation within a timeframe
   */
  private calculateTimeframeCorrelation(signals: MethodologySignal[]): number {
    if (signals.length < 2) return 0;

    let correlationSum = 0;
    let comparisons = 0;

    for (let i = 0; i < signals.length; i++) {
      for (let j = i + 1; j < signals.length; j++) {
        const signal1 = signals[i];
        const signal2 = signals[j];

        // Calculate correlation based on signal agreement and confidence similarity
        let correlation = 0;

        if (signal1.signal === signal2.signal) {
          correlation += 0.7; // Same signal direction
        }

        const confidenceDiff = Math.abs(signal1.confidence - signal2.confidence);
        correlation += (1 - confidenceDiff / 100) * 0.3; // Similar confidence

        correlationSum += correlation;
        comparisons++;
      }
    }

    return comparisons > 0 ? correlationSum / comparisons : 0;
  }

  /**
   * Calculate correlations between different timeframes
   */
  private calculateTimeframeCorrelations(analyses: MultiTimeframeAnalysis[]): TimeframeCorrelation[] {
    const correlations: TimeframeCorrelation[] = [];

    for (let i = 0; i < analyses.length; i++) {
      for (let j = i + 1; j < analyses.length; j++) {
        const analysis1 = analyses[i];
        const analysis2 = analyses[j];

        const correlation = this.calculateBiasCorrelation(analysis1.bias, analysis2.bias);
        const agreement = correlation > this.config.correlationThreshold;

        correlations.push({
          timeframe1: analysis1.timeframe,
          timeframe2: analysis2.timeframe,
          correlation,
          agreement
        });
      }
    }

    return correlations;
  }

  /**
   * Calculate correlation between two biases
   */
  private calculateBiasCorrelation(bias1: string, bias2: string): number {
    if (bias1 === bias2) {
      return bias1 === 'NEUTRAL' ? 0.5 : 1.0;
    }
    
    if ((bias1 === 'BULLISH' && bias2 === 'BEARISH') || 
        (bias1 === 'BEARISH' && bias2 === 'BULLISH')) {
      return 0.0;
    }
    
    return 0.3; // One is NEUTRAL, other is directional
  }

  /**
   * Calculate overall bias across all timeframes
   */
  private calculateOverallBias(analyses: MultiTimeframeAnalysis[]): 'BULLISH' | 'BEARISH' | 'NEUTRAL' {
    let bullishScore = 0;
    let bearishScore = 0;
    let totalWeight = 0;

    for (const analysis of analyses) {
      const timeframeConfig = this.config.timeframes.find(tf => tf.name === analysis.timeframe);
      const weight = timeframeConfig?.weight || 0.2;
      const biasWeight = this.config.higherTimeframeBias * (timeframeConfig?.priority || 1);

      totalWeight += weight * biasWeight;

      if (analysis.bias === 'BULLISH') {
        bullishScore += weight * biasWeight * (analysis.confidence / 100);
      } else if (analysis.bias === 'BEARISH') {
        bearishScore += weight * biasWeight * (analysis.confidence / 100);
      }
    }

    if (totalWeight === 0) return 'NEUTRAL';

    const normalizedBullish = bullishScore / totalWeight;
    const normalizedBearish = bearishScore / totalWeight;

    if (normalizedBullish > normalizedBearish && normalizedBullish > 0.6) {
      return 'BULLISH';
    } else if (normalizedBearish > normalizedBullish && normalizedBearish > 0.6) {
      return 'BEARISH';
    }

    return 'NEUTRAL';
  }

  /**
   * Calculate confluence score
   */
  private calculateConfluenceScore(
    analyses: MultiTimeframeAnalysis[],
    correlations: TimeframeCorrelation[]
  ): number {
    if (analyses.length === 0) return 0;

    // Calculate timeframe agreement
    const agreementCount = correlations.filter(corr => corr.agreement).length;
    const totalCorrelations = correlations.length;
    const agreementRatio = totalCorrelations > 0 ? agreementCount / totalCorrelations : 0;

    // Calculate average confidence across timeframes
    const avgConfidence = analyses.reduce((sum, analysis) => sum + analysis.confidence, 0) / analyses.length;

    // Calculate methodology confluence within timeframes
    const avgMethodologyCorrelation = analyses.reduce((sum, analysis) => sum + analysis.correlation, 0) / analyses.length;

    // Combine factors
    const confluenceScore = (
      agreementRatio * 0.4 +
      (avgConfidence / 100) * 0.3 +
      avgMethodologyCorrelation * 0.3
    ) * 100;

    return Math.min(confluenceScore, 100);
  }

  /**
   * Generate recommended signal based on multi-timeframe analysis
   */
  private generateRecommendedSignal(
    symbol: string,
    analyses: MultiTimeframeAnalysis[],
    overallBias: 'BULLISH' | 'BEARISH' | 'NEUTRAL',
    confluenceScore: number
  ): MethodologySignal {
    const primaryTimeframe = this.getPrimaryTimeframe(this.config.timeframes);
    
    // Find the primary timeframe analysis
    const primaryAnalysis = analyses.find(a => a.timeframe === primaryTimeframe);
    
    if (!primaryAnalysis || confluenceScore < this.config.confluenceRequirement * 100) {
      return {
        methodology: 'MultiTimeframe',
        confidence: 0,
        signal: 'NEUTRAL',
        strength: 0,
        reasoning: 'Insufficient confluence across timeframes',
        timeframe: primaryTimeframe,
        metadata: {
          overallBias,
          confluenceScore,
          timeframeCount: analyses.length
        }
      };
    }

    // Calculate weighted signal
    let signal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL';
    if (overallBias === 'BULLISH' && confluenceScore > 70) {
      signal = 'BUY';
    } else if (overallBias === 'BEARISH' && confluenceScore > 70) {
      signal = 'SELL';
    }

    // Calculate overall strength
    const avgStrength = analyses.reduce((sum, analysis) => sum + analysis.strength, 0) / analyses.length;

    // Generate reasoning
    const reasoning = this.generateMultiTimeframeReasoning(analyses, overallBias, confluenceScore);

    return {
      methodology: 'MultiTimeframe',
      confidence: confluenceScore,
      signal,
      strength: avgStrength,
      reasoning,
      timeframe: primaryTimeframe,
      metadata: {
        overallBias,
        confluenceScore,
        timeframeCount: analyses.length,
        analyses: analyses.map(a => ({
          timeframe: a.timeframe,
          bias: a.bias,
          confidence: a.confidence
        }))
      }
    };
  }

  /**
   * Generate reasoning for multi-timeframe signal
   */
  private generateMultiTimeframeReasoning(
    analyses: MultiTimeframeAnalysis[],
    overallBias: 'BULLISH' | 'BEARISH' | 'NEUTRAL',
    confluenceScore: number
  ): string {
    const reasons: string[] = [];

    reasons.push(`Overall bias: ${overallBias}`);
    reasons.push(`Confluence score: ${confluenceScore.toFixed(1)}%`);
    reasons.push(`Timeframes analyzed: ${analyses.length}`);

    const biasBreakdown = analyses.reduce((acc, analysis) => {
      acc[analysis.bias] = (acc[analysis.bias] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const biasString = Object.entries(biasBreakdown)
      .map(([bias, count]) => `${count} ${bias.toLowerCase()}`)
      .join(', ');
    
    reasons.push(`Bias breakdown: ${biasString}`);

    const highConfidenceTimeframes = analyses.filter(a => a.confidence > 70);
    if (highConfidenceTimeframes.length > 0) {
      reasons.push(`${highConfidenceTimeframes.length} high-confidence timeframes`);
    }

    return reasons.join('; ');
  }

  // Helper methods
  private getMethodologyConfig(methodology: string): any {
    switch (methodology) {
      case 'PriceAction': return config.methodologies.priceAction;
      case 'ICT': return config.methodologies.ict;
      case 'SMC': return config.methodologies.smc;
      case 'ElliottWave': return config.methodologies.elliottWave;
      case 'Wyckoff': return config.methodologies.wyckoff;
      default: return null;
    }
  }

  private getPrimaryTimeframe(timeframes: TimeframeConfig[]): string {
    const enabledTimeframes = timeframes.filter(tf => tf.enabled);
    const primaryTf = enabledTimeframes.reduce((primary, current) => 
      current.priority > primary.priority ? current : primary
    );
    return primaryTf.name;
  }

  private getEmptyResult(symbol: string): MultiTimeframeResult {
    return {
      primaryTimeframe: 'H1',
      analyses: [],
      correlations: [],
      overallBias: 'NEUTRAL',
      confluenceScore: 0,
      recommendedSignal: {
        methodology: 'MultiTimeframe',
        confidence: 0,
        signal: 'NEUTRAL',
        strength: 0,
        reasoning: 'Multi-timeframe analysis disabled or insufficient data',
        timeframe: 'H1',
        metadata: {}
      }
    };
  }

  /**
   * Get cached analysis result
   */
  public getCachedAnalysis(symbol: string): MultiTimeframeResult | null {
    return this.analysisCache.get(symbol) || null;
  }

  /**
   * Clear cache for a symbol
   */
  public clearCache(symbol?: string): void {
    if (symbol) {
      this.dataCache.delete(symbol);
      this.analysisCache.delete(symbol);
    } else {
      this.dataCache.clear();
      this.analysisCache.clear();
    }
  }
}

// Export singleton instance
export const multiTimeframeEngine = new MultiTimeframeEngine();
