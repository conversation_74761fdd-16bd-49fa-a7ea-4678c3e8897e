import { EventEmitter } from 'events';
import { config } from '../../config';
import {
  CandlestickData,
  MethodologySignal,
  TechnicalIndicators
} from '../../types';
import { logger } from '../../utils/logger';

// Service interfaces
export interface IMethodologyService {
  analyze(
    symbol: string,
    candles: CandlestickData[],
    timeframe: string,
    indicators?: TechnicalIndicators
  ): Promise<any>;
  
  generateSignal(
    symbol: string,
    analysis: any,
    currentPrice: number,
    timeframe: string
  ): MethodologySignal;
}

export interface IMultiTimeframeService {
  analyze(
    symbol: string,
    timeframeData: Map<string, CandlestickData[]>,
    indicators?: Map<string, TechnicalIndicators>
  ): Promise<any>;
}

// Service registry
export interface ServiceRegistry {
  priceAction: IMethodologyService;
  ict: IMethodologyService;
  smc: IMethodologyService;
  elliottWave: IMethodologyService;
  wyckoff: IMethodologyService;
  multiTimeframe: IMultiTimeframeService;
}

// Service configuration
export interface ServiceConfig {
  enabled: boolean;
  priority: number;
  weight: number;
  dependencies?: string[];
}

export interface ServiceContainerConfig {
  services: Record<string, ServiceConfig>;
  autoStart: boolean;
  healthCheckInterval: number;
}

/**
 * Service Container for managing methodology services
 */
export class ServiceContainer extends EventEmitter {
  private services: Map<string, IMethodologyService | IMultiTimeframeService> = new Map();
  private serviceConfigs: Map<string, ServiceConfig> = new Map();
  private serviceHealth: Map<string, boolean> = new Map();
  private initialized = false;
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(private containerConfig: ServiceContainerConfig) {
    super();
    this.setupServiceConfigs();
  }

  /**
   * Initialize the service container
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      logger.trading('warn', 'Service container already initialized');
      return;
    }

    try {
      logger.trading('info', 'Initializing service container...');

      // Register services in dependency order
      await this.registerServices();

      // Start health monitoring
      if (this.containerConfig.autoStart) {
        this.startHealthMonitoring();
      }

      this.initialized = true;
      this.emit('initialized');

      logger.trading('info', 'Service container initialized successfully', {
        servicesCount: this.services.size,
        enabledServices: Array.from(this.serviceConfigs.entries())
          .filter(([_, config]) => config.enabled)
          .map(([name]) => name)
      });
    } catch (error) {
      logger.trading('error', 'Failed to initialize service container', { error });
      throw error;
    }
  }

  /**
   * Register all methodology services
   */
  private async registerServices(): Promise<void> {
    // Import services dynamically to avoid circular dependencies
    const { priceActionService } = await import('./price-action');
    const { ictService } = await import('./ict');
    const { smcService } = await import('./smc');
    const { elliottWaveService } = await import('./elliott-wave');
    const { wyckoffService } = await import('./wyckoff');
    const { multiTimeframeEngine } = await import('./multi-timeframe');

    // Register services
    this.registerService('priceAction', priceActionService);
    this.registerService('ict', ictService);
    this.registerService('smc', smcService);
    this.registerService('elliottWave', elliottWaveService);
    this.registerService('wyckoff', wyckoffService);
    this.registerService('multiTimeframe', multiTimeframeEngine);

    // Validate dependencies
    this.validateDependencies();
  }

  /**
   * Register a single service
   */
  public registerService(
    name: string,
    service: IMethodologyService | IMultiTimeframeService
  ): void {
    const serviceConfig = this.serviceConfigs.get(name);
    
    if (!serviceConfig) {
      throw new Error(`No configuration found for service: ${name}`);
    }

    if (!serviceConfig.enabled) {
      logger.trading('debug', `Service ${name} is disabled, skipping registration`);
      return;
    }

    this.services.set(name, service);
    this.serviceHealth.set(name, true);

    logger.trading('debug', `Service registered: ${name}`, {
      priority: serviceConfig.priority,
      weight: serviceConfig.weight
    });

    this.emit('serviceRegistered', { name, service });
  }

  /**
   * Get a service by name
   */
  public getService<T extends IMethodologyService | IMultiTimeframeService>(
    name: string
  ): T | null {
    const service = this.services.get(name);
    return service as T || null;
  }

  /**
   * Get all methodology services
   */
  public getMethodologyServices(): Map<string, IMethodologyService> {
    const methodologyServices = new Map<string, IMethodologyService>();
    
    for (const [name, service] of this.services.entries()) {
      if (name !== 'multiTimeframe' && this.isMethodologyService(service)) {
        methodologyServices.set(name, service);
      }
    }
    
    return methodologyServices;
  }

  /**
   * Get multi-timeframe service
   */
  public getMultiTimeframeService(): IMultiTimeframeService | null {
    return this.getService<IMultiTimeframeService>('multiTimeframe');
  }

  /**
   * Check if a service is available and healthy
   */
  public isServiceHealthy(name: string): boolean {
    return this.serviceHealth.get(name) || false;
  }

  /**
   * Get service configuration
   */
  public getServiceConfig(name: string): ServiceConfig | null {
    return this.serviceConfigs.get(name) || null;
  }

  /**
   * Get all enabled services sorted by priority
   */
  public getEnabledServices(): Array<{ name: string; service: any; config: ServiceConfig }> {
    const enabledServices: Array<{ name: string; service: any; config: ServiceConfig }> = [];

    for (const [name, service] of this.services.entries()) {
      const config = this.serviceConfigs.get(name);
      if (config && config.enabled && this.serviceHealth.get(name)) {
        enabledServices.push({ name, service, config });
      }
    }

    // Sort by priority (higher priority first)
    return enabledServices.sort((a, b) => b.config.priority - a.config.priority);
  }

  /**
   * Perform health check on all services
   */
  public async performHealthCheck(): Promise<Map<string, boolean>> {
    const healthResults = new Map<string, boolean>();

    for (const [name, service] of this.services.entries()) {
      try {
        // Basic health check - ensure service has required methods
        const isHealthy = this.checkServiceHealth(name, service);
        healthResults.set(name, isHealthy);
        this.serviceHealth.set(name, isHealthy);

        if (!isHealthy) {
          logger.trading('warn', `Service health check failed: ${name}`);
          this.emit('serviceUnhealthy', { name, service });
        }
      } catch (error) {
        logger.trading('error', `Health check error for service ${name}`, { error });
        healthResults.set(name, false);
        this.serviceHealth.set(name, false);
        this.emit('serviceError', { name, service, error });
      }
    }

    return healthResults;
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(async () => {
      await this.performHealthCheck();
    }, this.containerConfig.healthCheckInterval);

    logger.trading('debug', 'Health monitoring started', {
      interval: this.containerConfig.healthCheckInterval
    });
  }

  /**
   * Stop health monitoring
   */
  public stopHealthMonitoring(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }
  }

  /**
   * Shutdown the service container
   */
  public async shutdown(): Promise<void> {
    logger.trading('info', 'Shutting down service container...');

    this.stopHealthMonitoring();
    
    // Emit shutdown event for services to clean up
    this.emit('shutdown');

    this.services.clear();
    this.serviceHealth.clear();
    this.initialized = false;

    logger.trading('info', 'Service container shut down successfully');
  }

  // Private helper methods
  private setupServiceConfigs(): void {
    // Set up default configurations based on app config
    this.serviceConfigs.set('priceAction', {
      enabled: config.methodologies.priceAction.enabled,
      priority: 5,
      weight: config.methodologies.priceAction.weight,
      dependencies: []
    });

    this.serviceConfigs.set('ict', {
      enabled: config.methodologies.ict.enabled,
      priority: 4,
      weight: config.methodologies.ict.weight,
      dependencies: []
    });

    this.serviceConfigs.set('smc', {
      enabled: config.methodologies.smc.enabled,
      priority: 4,
      weight: config.methodologies.smc.weight,
      dependencies: []
    });

    this.serviceConfigs.set('elliottWave', {
      enabled: config.methodologies.elliottWave.enabled,
      priority: 3,
      weight: config.methodologies.elliottWave.weight,
      dependencies: []
    });

    this.serviceConfigs.set('wyckoff', {
      enabled: config.methodologies.wyckoff.enabled,
      priority: 3,
      weight: config.methodologies.wyckoff.weight,
      dependencies: []
    });

    this.serviceConfigs.set('multiTimeframe', {
      enabled: config.multiTimeframe.enabled,
      priority: 1,
      weight: 1.0,
      dependencies: ['priceAction', 'ict', 'smc', 'elliottWave', 'wyckoff']
    });
  }

  private validateDependencies(): void {
    for (const [serviceName, config] of this.serviceConfigs.entries()) {
      if (!config.enabled) continue;

      if (config.dependencies) {
        for (const dependency of config.dependencies) {
          const depConfig = this.serviceConfigs.get(dependency);
          if (!depConfig || !depConfig.enabled) {
            logger.trading('warn', `Service ${serviceName} depends on disabled service ${dependency}`);
          }
        }
      }
    }
  }

  private checkServiceHealth(name: string, service: any): boolean {
    try {
      // Check if service has required methods
      if (name === 'multiTimeframe') {
        return typeof service.analyze === 'function';
      } else {
        return (
          typeof service.analyze === 'function' &&
          typeof service.generateSignal === 'function'
        );
      }
    } catch (error) {
      return false;
    }
  }

  private isMethodologyService(service: any): service is IMethodologyService {
    return (
      typeof service.analyze === 'function' &&
      typeof service.generateSignal === 'function'
    );
  }

  /**
   * Get service statistics
   */
  public getServiceStatistics(): {
    total: number;
    enabled: number;
    healthy: number;
    unhealthy: number;
  } {
    const total = this.services.size;
    const enabled = Array.from(this.serviceConfigs.values()).filter(config => config.enabled).length;
    const healthy = Array.from(this.serviceHealth.values()).filter(health => health).length;
    const unhealthy = total - healthy;

    return { total, enabled, healthy, unhealthy };
  }

  /**
   * Update service configuration
   */
  public updateServiceConfig(name: string, config: Partial<ServiceConfig>): void {
    const existingConfig = this.serviceConfigs.get(name);
    if (!existingConfig) {
      throw new Error(`Service ${name} not found`);
    }

    const updatedConfig = { ...existingConfig, ...config };
    this.serviceConfigs.set(name, updatedConfig);

    logger.trading('info', `Service configuration updated: ${name}`, updatedConfig);
    this.emit('serviceConfigUpdated', { name, config: updatedConfig });
  }

  /**
   * Check if container is initialized
   */
  public isInitialized(): boolean {
    return this.initialized;
  }
}

// Create and export singleton instance
const containerConfig: ServiceContainerConfig = {
  services: {},
  autoStart: true,
  healthCheckInterval: 60000 // 1 minute
};

export const serviceContainer = new ServiceContainer(containerConfig);
