import { EventEmitter } from 'events';
import { config } from '../../config';
import {
  CandlestickData,
  FairValueGap,
  ICTAnalysis,
  LiquidityPool,
  LiquiditySweep,
  MethodologySignal,
  OrderBlock,
  TechnicalIndicators
} from '../../types';
import { logger } from '../../utils/logger';

export interface ICTConfig {
  enabled: boolean;
  weight: number;
  minConfidence: number;
  orderBlockMinSize: number;
  fairValueGapMinSize: number;
  liquidityThreshold: number;
}

export class ICTService extends EventEmitter {
  private config: ICTConfig;
  private orderBlockHistory: Map<string, OrderBlock[]> = new Map();
  private fvgHistory: Map<string, FairValueGap[]> = new Map();
  private liquidityHistory: Map<string, LiquidityPool[]> = new Map();

  constructor() {
    super();
    this.config = config.methodologies.ict;
  }

  /**
   * Analyze ICT concepts for a given symbol and timeframe
   */
  public async analyze(
    symbol: string,
    candles: CandlestickData[],
    timeframe: string,
    indicators?: TechnicalIndicators
  ): Promise<ICTAnalysis> {
    try {
      if (!this.config.enabled || candles.length < 30) {
        return this.getEmptyAnalysis();
      }

      const analysis: ICTAnalysis = {
        marketStructure: await this.analyzeMarketStructure(candles),
        orderBlocks: await this.detectOrderBlocks(candles, timeframe),
        fairValueGaps: await this.detectFairValueGaps(candles, timeframe),
        liquidity: await this.analyzeLiquidity(candles, timeframe),
        institutionalLevels: await this.identifyInstitutionalLevels(candles),
        confidence: 0
      };

      // Calculate confidence after analysis is complete
      const currentPrice = candles[candles.length - 1].close;
      const signal = this.calculateSignal(analysis, currentPrice);
      analysis.confidence = this.calculateConfidence(analysis, signal);

      // Store analysis for future reference
      this.storeAnalysisHistory(symbol, analysis);

      logger.trading('debug', `ICT analysis completed for ${symbol}`, {
        timeframe,
        marketStructure: analysis.marketStructure.trend,
        orderBlocks:
          analysis.orderBlocks.bullish.length +
          analysis.orderBlocks.bearish.length,
        fairValueGaps: analysis.fairValueGaps.length,
        liquidityPools: analysis.liquidity.pools.length
      });

      return analysis;
    } catch (error) {
      logger.trading('error', `ICT analysis failed for ${symbol}`, { error });
      return this.getEmptyAnalysis();
    }
  }

  /**
   * Generate trading signal based on ICT analysis
   */
  public generateSignal(
    symbol: string,
    analysis: ICTAnalysis,
    currentPrice: number,
    timeframe: string
  ): MethodologySignal {
    try {
      const signal = this.calculateSignal(analysis, currentPrice);
      const confidence = this.calculateConfidence(analysis, signal);
      const reasoning = this.generateReasoning(analysis, signal, currentPrice);

      return {
        methodology: 'ICT',
        confidence,
        signal,
        strength: this.calculateStrength(analysis),
        reasoning,
        timeframe,
        metadata: {
          marketStructure: analysis.marketStructure,
          orderBlocks: analysis.orderBlocks,
          fairValueGaps: analysis.fairValueGaps,
          liquidity: analysis.liquidity,
          institutionalLevels: analysis.institutionalLevels
        }
      };
    } catch (error) {
      logger.trading('error', `ICT signal generation failed for ${symbol}`, {
        error
      });
      return this.getEmptySignal(timeframe);
    }
  }

  /**
   * Analyze market structure using ICT principles
   */
  private async analyzeMarketStructure(
    candles: CandlestickData[]
  ): Promise<ICTAnalysis['marketStructure']> {
    const recentCandles = candles.slice(-50);
    const highs = recentCandles.map((c) => c.high);
    const lows = recentCandles.map((c) => c.low);

    // Identify swing highs and lows
    const swingHighs = this.identifySwingPoints(highs, 'high');
    const swingLows = this.identifySwingPoints(lows, 'low');

    // Determine trend based on structure breaks
    let trend: 'bullish' | 'bearish' | 'ranging' = 'ranging';
    let structureBreak = false;
    let confirmation = false;

    // Check for bullish structure (higher highs and higher lows)
    if (swingHighs.length >= 2 && swingLows.length >= 2) {
      const recentHighs = swingHighs.slice(-2);
      const recentLows = swingLows.slice(-2);

      if (
        recentHighs[1].value > recentHighs[0].value &&
        recentLows[1].value > recentLows[0].value
      ) {
        trend = 'bullish';
        structureBreak = true;
        confirmation = this.confirmStructureBreak(recentCandles, 'bullish');
      } else if (
        recentHighs[1].value < recentHighs[0].value &&
        recentLows[1].value < recentLows[0].value
      ) {
        trend = 'bearish';
        structureBreak = true;
        confirmation = this.confirmStructureBreak(recentCandles, 'bearish');
      }
    }

    return {
      trend,
      higherHighs: trend === 'bullish',
      higherLows: trend === 'bullish',
      lowerHighs: trend === 'bearish',
      lowerLows: trend === 'bearish',
      structureBreak,
      confirmation
    };
  }

  /**
   * Detect order blocks (institutional order areas)
   */
  private async detectOrderBlocks(
    candles: CandlestickData[],
    timeframe: string
  ): Promise<ICTAnalysis['orderBlocks']> {
    const bullish: OrderBlock[] = [];
    const bearish: OrderBlock[] = [];

    // Look for order blocks in recent candles
    for (let i = 5; i < candles.length - 1; i++) {
      const current = candles[i];
      const next = candles[i + 1];
      const bodySize = Math.abs(current.close - current.open);
      const range = current.high - current.low;

      // Minimum size requirement
      if (bodySize < this.config.orderBlockMinSize * current.close) continue;

      // Bullish order block: strong bullish candle followed by gap up
      if (current.close > current.open && next.open > current.high) {
        const orderBlock: OrderBlock = {
          id: `${timeframe}_${current.timestamp}_bullish`,
          price: (current.open + current.close) / 2,
          timestamp: current.timestamp,
          type: 'bullish',
          strength: this.calculateOrderBlockStrength(
            current,
            candles.slice(i - 5, i)
          ),
          tested: false,
          volume: current.volume || 1,
          timeframe
        };

        // Check if it has been tested
        orderBlock.tested = this.hasBeenTested(
          orderBlock,
          candles.slice(i + 1)
        );
        bullish.push(orderBlock);
      }

      // Bearish order block: strong bearish candle followed by gap down
      if (current.close < current.open && next.open < current.low) {
        const orderBlock: OrderBlock = {
          id: `${timeframe}_${current.timestamp}_bearish`,
          price: (current.open + current.close) / 2,
          timestamp: current.timestamp,
          type: 'bearish',
          strength: this.calculateOrderBlockStrength(
            current,
            candles.slice(i - 5, i)
          ),
          tested: false,
          volume: current.volume || 1,
          timeframe
        };

        // Check if it has been tested
        orderBlock.tested = this.hasBeenTested(
          orderBlock,
          candles.slice(i + 1)
        );
        bearish.push(orderBlock);
      }
    }

    return {
      bullish: bullish.slice(-10), // Keep last 10
      bearish: bearish.slice(-10)
    };
  }

  /**
   * Detect Fair Value Gaps (FVG)
   */
  private async detectFairValueGaps(
    candles: CandlestickData[],
    timeframe: string
  ): Promise<FairValueGap[]> {
    const fairValueGaps: FairValueGap[] = [];

    for (let i = 1; i < candles.length - 1; i++) {
      const prev = candles[i - 1];
      const current = candles[i];
      const next = candles[i + 1];

      // Bullish FVG: gap between prev.high and next.low
      if (prev.high < next.low && current.close > current.open) {
        const gapSize = next.low - prev.high;
        if (gapSize >= this.config.fairValueGapMinSize * current.close) {
          fairValueGaps.push({
            id: `${timeframe}_${current.timestamp}_bullish_fvg`,
            high: next.low,
            low: prev.high,
            timestamp: current.timestamp,
            type: 'bullish',
            filled: this.isFVGFilled(prev.high, next.low, candles.slice(i + 1)),
            strength: gapSize / current.close,
            timeframe
          });
        }
      }

      // Bearish FVG: gap between prev.low and next.high
      if (prev.low > next.high && current.close < current.open) {
        const gapSize = prev.low - next.high;
        if (gapSize >= this.config.fairValueGapMinSize * current.close) {
          fairValueGaps.push({
            id: `${timeframe}_${current.timestamp}_bearish_fvg`,
            high: prev.low,
            low: next.high,
            timestamp: current.timestamp,
            type: 'bearish',
            filled: this.isFVGFilled(next.high, prev.low, candles.slice(i + 1)),
            strength: gapSize / current.close,
            timeframe
          });
        }
      }
    }

    return fairValueGaps.slice(-20); // Keep last 20
  }

  /**
   * Analyze liquidity pools and sweeps
   */
  private async analyzeLiquidity(
    candles: CandlestickData[],
    timeframe: string
  ): Promise<ICTAnalysis['liquidity']> {
    const pools: LiquidityPool[] = [];
    const sweeps: LiquiditySweep[] = [];

    // Identify liquidity pools at swing highs and lows
    const recentCandles = candles.slice(-100);

    for (let i = 10; i < recentCandles.length - 10; i++) {
      const current = recentCandles[i];
      const before = recentCandles.slice(i - 10, i);
      const after = recentCandles.slice(i + 1, i + 11);

      // Liquidity pool at swing high
      if (this.isSwingHigh(current, before, after)) {
        pools.push({
          id: `${timeframe}_${current.timestamp}_sell_pool`,
          price: current.high,
          type: 'sell',
          strength: this.calculateLiquidityStrength(current, before, after),
          timestamp: current.timestamp,
          timeframe
        });

        // Check for liquidity sweep
        const sweep = this.detectLiquiditySweep(
          current.high,
          'up',
          candles.slice(i + 1)
        );
        if (sweep) sweeps.push(sweep);
      }

      // Liquidity pool at swing low
      if (this.isSwingLow(current, before, after)) {
        pools.push({
          id: `${timeframe}_${current.timestamp}_buy_pool`,
          price: current.low,
          type: 'buy',
          strength: this.calculateLiquidityStrength(current, before, after),
          timestamp: current.timestamp,
          timeframe
        });

        // Check for liquidity sweep
        const sweep = this.detectLiquiditySweep(
          current.low,
          'down',
          candles.slice(i + 1)
        );
        if (sweep) sweeps.push(sweep);
      }
    }

    return {
      pools: pools.slice(-15),
      sweeps: sweeps.slice(-10)
    };
  }

  /**
   * Identify institutional levels
   */
  private async identifyInstitutionalLevels(
    candles: CandlestickData[]
  ): Promise<
    {
      price: number;
      type: 'support' | 'resistance';
      strength: number;
      timestamp: number;
    }[]
  > {
    const levels: {
      price: number;
      type: 'support' | 'resistance';
      strength: number;
      timestamp: number;
    }[] = [];
    const recentCandles = candles.slice(-200);
    const currentPrice = candles[candles.length - 1].close;
    const currentTimestamp = candles[candles.length - 1].timestamp;

    // Find levels with multiple touches
    const priceMap = new Map<number, number>();
    const tolerance = 0.0001; // 1 pip tolerance

    for (const candle of recentCandles) {
      const prices = [candle.high, candle.low, candle.open, candle.close];

      for (const price of prices) {
        const roundedPrice = Math.round(price / tolerance) * tolerance;
        priceMap.set(roundedPrice, (priceMap.get(roundedPrice) || 0) + 1);
      }
    }

    // Filter levels with significant touches
    for (const [price, touches] of priceMap.entries()) {
      if (touches >= 5) {
        // Minimum 5 touches
        levels.push({
          price,
          type: price > currentPrice ? 'resistance' : 'support',
          strength: Math.min(touches / 10, 1),
          timestamp: currentTimestamp
        });
      }
    }

    return levels.sort((a, b) => a.price - b.price).slice(-20);
  }

  // Helper methods
  private identifySwingPoints(
    values: number[],
    type: 'high' | 'low'
  ): Array<{ index: number; value: number }> {
    const swingPoints: Array<{ index: number; value: number }> = [];

    for (let i = 2; i < values.length - 2; i++) {
      if (type === 'high') {
        if (
          values[i] > values[i - 1] &&
          values[i] > values[i - 2] &&
          values[i] > values[i + 1] &&
          values[i] > values[i + 2]
        ) {
          swingPoints.push({ index: i, value: values[i] });
        }
      } else {
        if (
          values[i] < values[i - 1] &&
          values[i] < values[i - 2] &&
          values[i] < values[i + 1] &&
          values[i] < values[i + 2]
        ) {
          swingPoints.push({ index: i, value: values[i] });
        }
      }
    }

    return swingPoints;
  }

  private confirmStructureBreak(
    candles: CandlestickData[],
    direction: 'bullish' | 'bearish'
  ): boolean {
    const recentCandles = candles.slice(-5);
    let confirmationCount = 0;

    for (const candle of recentCandles) {
      if (direction === 'bullish' && candle.close > candle.open) {
        confirmationCount++;
      } else if (direction === 'bearish' && candle.close < candle.open) {
        confirmationCount++;
      }
    }

    return confirmationCount >= 3;
  }

  private calculateOrderBlockStrength(
    candle: CandlestickData,
    context: CandlestickData[]
  ): number {
    const bodySize = Math.abs(candle.close - candle.open);
    const avgBodySize =
      context.reduce((sum, c) => sum + Math.abs(c.close - c.open), 0) /
      context.length;
    const volume = candle.volume || 1;
    const avgVolume =
      context.reduce((sum, c) => sum + (c.volume || 1), 0) / context.length;

    return Math.min((bodySize / avgBodySize) * (volume / avgVolume), 2) / 2;
  }

  private hasBeenTested(
    orderBlock: OrderBlock,
    futureCandles: CandlestickData[]
  ): boolean {
    const tolerance = 0.0002; // 2 pips tolerance

    return futureCandles.some((candle) => {
      if (orderBlock.type === 'bullish') {
        return (
          candle.low <= orderBlock.price * (1 + tolerance) &&
          candle.low >= orderBlock.price * (1 - tolerance)
        );
      } else {
        return (
          candle.high >= orderBlock.price * (1 - tolerance) &&
          candle.high <= orderBlock.price * (1 + tolerance)
        );
      }
    });
  }

  private isFVGFilled(
    low: number,
    high: number,
    futureCandles: CandlestickData[]
  ): boolean {
    return futureCandles.some(
      (candle) => candle.low <= low && candle.high >= high
    );
  }

  private isSwingHigh(
    candle: CandlestickData,
    before: CandlestickData[],
    after: CandlestickData[]
  ): boolean {
    return (
      before.every((c) => c.high < candle.high) &&
      after.every((c) => c.high < candle.high)
    );
  }

  private isSwingLow(
    candle: CandlestickData,
    before: CandlestickData[],
    after: CandlestickData[]
  ): boolean {
    return (
      before.every((c) => c.low > candle.low) &&
      after.every((c) => c.low > candle.low)
    );
  }

  private calculateLiquidityStrength(
    candle: CandlestickData,
    before: CandlestickData[],
    after: CandlestickData[]
  ): number {
    const volume = candle.volume || 1;
    const avgVolume =
      [...before, ...after].reduce((sum, c) => sum + (c.volume || 1), 0) /
      (before.length + after.length);
    const priceSignificance = Math.abs(candle.high - candle.low) / candle.close;

    return Math.min((volume / avgVolume) * priceSignificance * 10, 1);
  }

  private detectLiquiditySweep(
    price: number,
    direction: 'up' | 'down',
    futureCandles: CandlestickData[]
  ): LiquiditySweep | null {
    const tolerance = 0.0001;

    for (let i = 0; i < Math.min(futureCandles.length, 20); i++) {
      const candle = futureCandles[i];

      if (direction === 'up' && candle.high > price * (1 + tolerance)) {
        return {
          id: `sweep_${candle.timestamp}_up`,
          price: candle.high,
          direction,
          timestamp: candle.timestamp,
          confirmed: i < 10, // Confirmed if within 10 candles
          volume: candle.volume || 1,
          timeframe: ''
        };
      } else if (direction === 'down' && candle.low < price * (1 - tolerance)) {
        return {
          id: `sweep_${candle.timestamp}_down`,
          price: candle.low,
          direction,
          timestamp: candle.timestamp,
          confirmed: i < 10,
          volume: candle.volume || 1,
          timeframe: ''
        };
      }
    }

    return null;
  }

  private calculateSignal(
    analysis: ICTAnalysis,
    currentPrice: number
  ): 'BUY' | 'SELL' | 'NEUTRAL' {
    let bullishScore = 0;
    let bearishScore = 0;

    // Market structure
    if (
      analysis.marketStructure.trend === 'bullish' &&
      analysis.marketStructure.confirmation
    ) {
      bullishScore += 0.4;
    } else if (
      analysis.marketStructure.trend === 'bearish' &&
      analysis.marketStructure.confirmation
    ) {
      bearishScore += 0.4;
    }

    // Order blocks
    const nearBullishOB = analysis.orderBlocks.bullish.some(
      (ob) =>
        Math.abs(currentPrice - ob.price) / currentPrice < 0.001 && !ob.tested
    );
    const nearBearishOB = analysis.orderBlocks.bearish.some(
      (ob) =>
        Math.abs(currentPrice - ob.price) / currentPrice < 0.001 && !ob.tested
    );

    if (nearBullishOB) bullishScore += 0.3;
    if (nearBearishOB) bearishScore += 0.3;

    // Fair Value Gaps
    const inBullishFVG = analysis.fairValueGaps.some(
      (fvg) =>
        fvg.type === 'bullish' &&
        currentPrice >= fvg.low &&
        currentPrice <= fvg.high &&
        !fvg.filled
    );
    const inBearishFVG = analysis.fairValueGaps.some(
      (fvg) =>
        fvg.type === 'bearish' &&
        currentPrice >= fvg.low &&
        currentPrice <= fvg.high &&
        !fvg.filled
    );

    if (inBullishFVG) bullishScore += 0.2;
    if (inBearishFVG) bearishScore += 0.2;

    // Liquidity sweeps
    const recentBullishSweep = analysis.liquidity.sweeps.some(
      (sweep) =>
        sweep.direction === 'up' &&
        sweep.confirmed &&
        Date.now() - sweep.timestamp < 3600000
    );
    const recentBearishSweep = analysis.liquidity.sweeps.some(
      (sweep) =>
        sweep.direction === 'down' &&
        sweep.confirmed &&
        Date.now() - sweep.timestamp < 3600000
    );

    if (recentBullishSweep) bullishScore += 0.1;
    if (recentBearishSweep) bearishScore += 0.1;

    if (bullishScore > bearishScore && bullishScore > 0.6) {
      return 'BUY';
    } else if (bearishScore > bullishScore && bearishScore > 0.6) {
      return 'SELL';
    }
    return 'NEUTRAL';
  }

  private calculateConfidence(
    analysis: ICTAnalysis,
    signal: 'BUY' | 'SELL' | 'NEUTRAL'
  ): number {
    if (signal === 'NEUTRAL') return 0;

    let confidence = 0;

    // Market structure confirmation
    if (analysis.marketStructure.confirmation) confidence += 30;

    // Order block quality
    const relevantOBs =
      signal === 'BUY'
        ? analysis.orderBlocks.bullish
        : analysis.orderBlocks.bearish;
    const strongOBs = relevantOBs.filter(
      (ob) => ob.strength > 0.7 && !ob.tested
    );
    confidence += Math.min(strongOBs.length * 15, 30);

    // FVG presence
    const relevantFVGs = analysis.fairValueGaps.filter(
      (fvg) =>
        (signal === 'BUY' && fvg.type === 'bullish') ||
        (signal === 'SELL' && fvg.type === 'bearish')
    );
    confidence += Math.min(relevantFVGs.length * 10, 20);

    // Liquidity analysis
    const strongLiquidity = analysis.liquidity.pools.filter(
      (pool) => pool.strength > this.config.liquidityThreshold
    );
    confidence += Math.min(strongLiquidity.length * 5, 20);

    return Math.min(confidence, 100);
  }

  private calculateStrength(analysis: ICTAnalysis): number {
    let strength = 0;

    // Market structure strength
    if (analysis.marketStructure.structureBreak) strength += 0.3;
    if (analysis.marketStructure.confirmation) strength += 0.2;

    // Order block strength
    const avgOBStrength =
      [...analysis.orderBlocks.bullish, ...analysis.orderBlocks.bearish].reduce(
        (sum, ob) => sum + ob.strength,
        0
      ) /
      (analysis.orderBlocks.bullish.length +
        analysis.orderBlocks.bearish.length || 1);
    strength += avgOBStrength * 0.3;

    // Liquidity strength
    const avgLiquidityStrength =
      analysis.liquidity.pools.reduce((sum, pool) => sum + pool.strength, 0) /
      (analysis.liquidity.pools.length || 1);
    strength += avgLiquidityStrength * 0.2;

    return Math.min(strength, 1);
  }

  private generateReasoning(
    analysis: ICTAnalysis,
    signal: 'BUY' | 'SELL' | 'NEUTRAL',
    currentPrice: number
  ): string {
    const reasons: string[] = [];

    if (analysis.marketStructure.trend !== 'ranging') {
      reasons.push(
        `Market structure: ${analysis.marketStructure.trend} ${
          analysis.marketStructure.confirmation
            ? '(confirmed)'
            : '(unconfirmed)'
        }`
      );
    }

    const totalOBs =
      analysis.orderBlocks.bullish.length + analysis.orderBlocks.bearish.length;
    if (totalOBs > 0) {
      reasons.push(`${totalOBs} order blocks identified`);
    }

    if (analysis.fairValueGaps.length > 0) {
      const unfilledFVGs = analysis.fairValueGaps.filter(
        (fvg) => !fvg.filled
      ).length;
      reasons.push(
        `${unfilledFVGs}/${analysis.fairValueGaps.length} unfilled FVGs`
      );
    }

    if (analysis.liquidity.pools.length > 0) {
      reasons.push(
        `${analysis.liquidity.pools.length} liquidity pools, ${analysis.liquidity.sweeps.length} sweeps`
      );
    }

    return reasons.join('; ') || 'ICT analysis inconclusive';
  }

  private storeAnalysisHistory(symbol: string, analysis: ICTAnalysis): void {
    // Store order blocks
    if (!this.orderBlockHistory.has(symbol)) {
      this.orderBlockHistory.set(symbol, []);
    }
    const obHistory = this.orderBlockHistory.get(symbol)!;
    obHistory.push(
      ...analysis.orderBlocks.bullish,
      ...analysis.orderBlocks.bearish
    );
    if (obHistory.length > 100) {
      obHistory.splice(0, obHistory.length - 100);
    }

    // Store FVGs
    if (!this.fvgHistory.has(symbol)) {
      this.fvgHistory.set(symbol, []);
    }
    const fvgHistory = this.fvgHistory.get(symbol)!;
    fvgHistory.push(...analysis.fairValueGaps);
    if (fvgHistory.length > 50) {
      fvgHistory.splice(0, fvgHistory.length - 50);
    }

    // Store liquidity pools
    if (!this.liquidityHistory.has(symbol)) {
      this.liquidityHistory.set(symbol, []);
    }
    const liquidityHistory = this.liquidityHistory.get(symbol)!;
    liquidityHistory.push(...analysis.liquidity.pools);
    if (liquidityHistory.length > 50) {
      liquidityHistory.splice(0, liquidityHistory.length - 50);
    }
  }

  private getEmptyAnalysis(): ICTAnalysis {
    return {
      marketStructure: {
        trend: 'ranging',
        higherHighs: false,
        higherLows: false,
        lowerHighs: false,
        lowerLows: false,
        structureBreak: false,
        confirmation: false
      },
      orderBlocks: {
        bullish: [],
        bearish: []
      },
      fairValueGaps: [],
      liquidity: {
        pools: [],
        sweeps: []
      },
      institutionalLevels: [],
      confidence: 0
    };
  }

  private getEmptySignal(timeframe: string): MethodologySignal {
    return {
      methodology: 'ICT',
      confidence: 0,
      signal: 'NEUTRAL',
      strength: 0,
      reasoning: 'ICT analysis disabled or insufficient data',
      timeframe,
      metadata: {}
    };
  }
}

// Export singleton instance
export const ictService = new ICTService();
