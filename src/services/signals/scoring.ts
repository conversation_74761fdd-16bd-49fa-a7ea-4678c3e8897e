import {
  AdvancedSignal,
  EnhancedSignalAnalysis,
  MethodologySignal,
  RiskAssessment,
  SignalConfluence
} from '../../types';
import { logger } from '../../utils/logger';

export interface SignalScoringConfig {
  methodologyWeights: {
    priceAction: number;
    ict: number;
    smc: number;
    elliottWave: number;
    wyckoff: number;
  };
  timeframeWeights: {
    '1m': number;
    '5m': number;
    '15m': number;
    '30m': number;
    '1H': number;
    '4H': number;
    '1D': number;
  };
  confluenceThresholds: {
    minimum: number;
    strong: number;
    veryStrong: number;
  };
  riskFactors: {
    volatility: number;
    spread: number;
    liquidity: number;
  };
}

export class AdvancedSignalScoring {
  private config: SignalScoringConfig;

  constructor(config?: Partial<SignalScoringConfig>) {
    this.config = {
      methodologyWeights: {
        priceAction: 0.25,
        ict: 0.2,
        smc: 0.2,
        elliottWave: 0.15,
        wyckoff: 0.2
      },
      timeframeWeights: {
        '1m': 0.05,
        '5m': 0.1,
        '15m': 0.15,
        '30m': 0.2,
        '1H': 0.25,
        '4H': 0.2,
        '1D': 0.05
      },
      confluenceThresholds: {
        minimum: 60,
        strong: 75,
        veryStrong: 85
      },
      riskFactors: {
        volatility: 0.3,
        spread: 0.3,
        liquidity: 0.4
      },
      ...config
    };
  }

  /**
   * Calculate comprehensive signal score
   */
  public calculateSignalScore(
    signal: AdvancedSignal,
    methodologySignals: MethodologySignal[],
    marketConditions?: any
  ): EnhancedSignalAnalysis {
    try {
      // Calculate methodology confluence
      const confluence = this.calculateMethodologyConfluence(
        methodologySignals,
        signal.type
      );

      // Calculate timeframe alignment
      const timeframeAlignment = this.calculateTimeframeAlignment(signal);

      // Calculate risk assessment
      const riskAssessment = this.calculateRiskAssessment(
        signal,
        marketConditions
      );

      // Calculate overall confidence
      const overallConfidence = this.calculateOverallConfidence(
        confluence,
        timeframeAlignment,
        riskAssessment,
        signal.confidence
      );

      // Generate enhanced analysis
      const enhancedAnalysis: EnhancedSignalAnalysis = {
        signal,
        confluence,
        timeframeAlignment,
        riskAssessment,
        overallConfidence,
        recommendation: this.generateRecommendation(
          overallConfidence,
          riskAssessment
        ),
        metadata: {
          calculatedAt: Date.now(),
          methodologyCount: methodologySignals.length,
          primaryTimeframe: this.getPrimaryTimeframe(signal),
          riskLevel: this.getRiskLevel(riskAssessment.score)
        }
      };

      logger.info('signals', `Signal score calculated for ${signal.symbol}`, {
        signalId: signal.id,
        overallConfidence,
        confluenceScore: confluence.score,
        riskScore: riskAssessment.score
      });

      return enhancedAnalysis;
    } catch (error) {
      logger.error('signals', 'Error calculating signal score', error);
      throw error;
    }
  }

  /**
   * Calculate methodology confluence
   */
  private calculateMethodologyConfluence(
    methodologySignals: MethodologySignal[],
    signalType: 'BUY' | 'SELL'
  ): SignalConfluence {
    const agreementSignals = methodologySignals.filter(
      (s) => s.signal === signalType
    );
    const totalSignals = methodologySignals.length;

    if (totalSignals === 0) {
      return {
        score: 0,
        agreementRatio: 0,
        methodologyBreakdown: {},
        strength: 'weak'
      };
    }

    // Calculate weighted agreement
    let weightedAgreement = 0;
    let totalWeight = 0;
    const methodologyBreakdown: Record<
      string,
      { agrees: boolean; confidence: number; weight: number }
    > = {};

    agreementSignals.forEach((signal) => {
      const weight = this.getMethodologyWeight(signal.methodology);
      weightedAgreement += signal.confidence * weight;
      totalWeight += weight;

      methodologyBreakdown[signal.methodology] = {
        agrees: true,
        confidence: signal.confidence,
        weight
      };
    });

    // Add disagreeing methodologies
    methodologySignals
      .filter((s) => s.signal !== signalType)
      .forEach((signal) => {
        const weight = this.getMethodologyWeight(signal.methodology);
        methodologyBreakdown[signal.methodology] = {
          agrees: false,
          confidence: signal.confidence,
          weight
        };
      });

    const agreementRatio = agreementSignals.length / totalSignals;
    const score =
      totalWeight > 0 ? (weightedAgreement / totalWeight) * agreementRatio : 0;

    return {
      score,
      agreementRatio,
      methodologyBreakdown,
      strength: this.getConfluenceStrength(score)
    };
  }

  /**
   * Calculate timeframe alignment
   */
  private calculateTimeframeAlignment(signal: AdvancedSignal): {
    score: number;
    alignedTimeframes: string[];
    conflictingTimeframes: string[];
    primaryTimeframe: string;
  } {
    const timeframes = Object.keys(signal.multiTimeframe || {});
    const primaryTimeframe = this.getPrimaryTimeframe(signal);

    // For now, return basic alignment based on available timeframes
    // In a real implementation, this would analyze actual timeframe data
    const alignedTimeframes = timeframes.filter(
      (tf) =>
        this.config.timeframeWeights[
          tf as keyof typeof this.config.timeframeWeights
        ] > 0.15
    );

    const conflictingTimeframes = timeframes.filter(
      (tf) => !alignedTimeframes.includes(tf)
    );

    const score =
      alignedTimeframes.length > 0
        ? (alignedTimeframes.length / timeframes.length) * 100
        : 50;

    return {
      score,
      alignedTimeframes,
      conflictingTimeframes,
      primaryTimeframe
    };
  }

  /**
   * Calculate risk assessment
   */
  private calculateRiskAssessment(
    signal: AdvancedSignal,
    marketConditions?: any
  ): RiskAssessment {
    const riskFactors: Record<string, number> = {};

    // Risk/Reward ratio assessment
    const rrRatio = signal.riskRewardRatio || 1;
    riskFactors.riskReward = Math.min(100, (rrRatio / 2) * 100);

    // Stop loss distance assessment
    const stopLossDistance =
      Math.abs(signal.entryPrice - signal.stopLoss) / signal.entryPrice;
    riskFactors.stopLossDistance = Math.max(0, 100 - stopLossDistance * 10000); // Assuming pip-based

    // Market volatility (if available)
    if (marketConditions?.volatility) {
      riskFactors.volatility = Math.max(0, 100 - marketConditions.volatility);
    } else {
      riskFactors.volatility = 70; // Default moderate risk
    }

    // Spread assessment (if available)
    if (marketConditions?.spread) {
      riskFactors.spread = Math.max(0, 100 - marketConditions.spread * 100);
    } else {
      riskFactors.spread = 80; // Default good spread
    }

    // Calculate overall risk score
    const score = Object.entries(riskFactors).reduce(
      (total, [factor, value]) => {
        const weight = 0.25; // Default weight since this is a static function
        return total + value * weight;
      },
      0
    );

    return {
      score,
      factors: riskFactors,
      level: this.getRiskLevel(score),
      recommendation:
        score > 70 ? 'acceptable' : score > 50 ? 'moderate' : 'high_risk'
    };
  }

  /**
   * Calculate overall confidence
   */
  private calculateOverallConfidence(
    confluence: SignalConfluence,
    timeframeAlignment: any,
    riskAssessment: RiskAssessment,
    baseConfidence: number
  ): number {
    const confluenceWeight = 0.4;
    const timeframeWeight = 0.3;
    const riskWeight = 0.2;
    const baseWeight = 0.1;

    const overallConfidence =
      confluence.score * confluenceWeight +
      timeframeAlignment.score * timeframeWeight +
      riskAssessment.score * riskWeight +
      baseConfidence * baseWeight;

    return Math.min(100, Math.max(0, overallConfidence));
  }

  /**
   * Generate trading recommendation
   */
  private generateRecommendation(
    confidence: number,
    riskAssessment: RiskAssessment
  ):
    | 'strong_buy'
    | 'buy'
    | 'weak_buy'
    | 'hold'
    | 'weak_sell'
    | 'sell'
    | 'strong_sell' {
    if (riskAssessment.score < 40) {
      return 'hold'; // Too risky regardless of confidence
    }

    if (confidence >= this.config.confluenceThresholds.veryStrong) {
      return 'strong_buy';
    } else if (confidence >= this.config.confluenceThresholds.strong) {
      return 'buy';
    } else if (confidence >= this.config.confluenceThresholds.minimum) {
      return 'weak_buy';
    } else {
      return 'hold';
    }
  }

  /**
   * Helper methods
   */
  private getMethodologyWeight(methodology: string): number {
    const methodologyKey = methodology
      .toLowerCase()
      .replace('_', '') as keyof typeof this.config.methodologyWeights;
    return this.config.methodologyWeights[methodologyKey] || 0.1;
  }

  private getConfluenceStrength(
    score: number
  ): 'weak' | 'moderate' | 'strong' | 'very_strong' {
    if (score >= this.config.confluenceThresholds.veryStrong)
      return 'very_strong';
    if (score >= this.config.confluenceThresholds.strong) return 'strong';
    if (score >= this.config.confluenceThresholds.minimum) return 'moderate';
    return 'weak';
  }

  private getPrimaryTimeframe(signal: AdvancedSignal): string {
    const timeframes = Object.keys(signal.multiTimeframe || {});
    return (
      timeframes.find(
        (tf) =>
          this.config.timeframeWeights[
            tf as keyof typeof this.config.timeframeWeights
          ] > 0.2
      ) || '1H'
    );
  }

  private getRiskLevel(
    score: number
  ): 'low' | 'moderate' | 'high' | 'very_high' {
    if (score >= 80) return 'low';
    if (score >= 60) return 'moderate';
    if (score >= 40) return 'high';
    return 'very_high';
  }

  /**
   * Update scoring configuration
   */
  public updateConfig(newConfig: Partial<SignalScoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('signals', 'Signal scoring configuration updated');
  }

  /**
   * Get current configuration
   */
  public getConfig(): SignalScoringConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const advancedSignalScoring = new AdvancedSignalScoring();
