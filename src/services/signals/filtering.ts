import {
  AdvancedSignal,
  EnhancedSignalAnalysis,
  RiskAssessment
} from '../../types';
import { logger } from '../../utils/logger';

export interface SignalFilterConfig {
  minConfidence: number;
  maxRiskLevel: 'low' | 'moderate' | 'high' | 'very_high';
  requiredMethodologies: number;
  blacklistPairs: string[];
  maxSignalsPerHour: number;
  riskRewardRatio: {
    minimum: number;
    preferred: number;
  };
  marketConditions: {
    maxSpread: number;
    minVolume: number;
    maxVolatility: number;
  };
  timeRestrictions: {
    avoidNews: boolean;
    tradingHours: {
      start: string; // HH:MM format
      end: string; // HH:MM format
    };
    avoidWeekends: boolean;
  };
}

export interface FilterResult {
  passed: boolean;
  reasons: string[];
  score: number;
  recommendation: 'accept' | 'review' | 'reject';
}

export class SignalFilteringService {
  private config: SignalFilterConfig;
  private signalHistory: Map<string, AdvancedSignal[]> = new Map();
  private readonly maxHistoryHours = 24;

  constructor(config?: Partial<SignalFilterConfig>) {
    this.config = {
      minConfidence: 70,
      maxRiskLevel: 'moderate',
      requiredMethodologies: 2,
      blacklistPairs: [],
      maxSignalsPerHour: 3,
      riskRewardRatio: {
        minimum: 1.5,
        preferred: 2.0
      },
      marketConditions: {
        maxSpread: 0.0003, // 3 pips for major pairs
        minVolume: 1000,
        maxVolatility: 80
      },
      timeRestrictions: {
        avoidNews: true,
        tradingHours: {
          start: '08:00',
          end: '18:00'
        },
        avoidWeekends: true
      },
      ...config
    };
  }

  /**
   * Apply comprehensive signal filtering
   */
  public filterSignal(
    signal: AdvancedSignal,
    enhancedAnalysis?: EnhancedSignalAnalysis,
    marketData?: any
  ): FilterResult {
    const reasons: string[] = [];
    let score = 100;

    try {
      // Basic signal validation
      if (!this.validateBasicSignal(signal, reasons)) {
        score -= 30;
      }

      // Confidence threshold check
      if (!this.checkConfidenceThreshold(signal, reasons)) {
        score -= 25;
      }

      // Risk assessment check
      if (
        enhancedAnalysis &&
        !this.checkRiskAssessment(enhancedAnalysis.riskAssessment, reasons)
      ) {
        score -= 20;
      }

      // Risk/Reward ratio check
      if (!this.checkRiskRewardRatio(signal, reasons)) {
        score -= 15;
      }

      // Market conditions check
      if (marketData && !this.checkMarketConditions(marketData, reasons)) {
        score -= 15;
      }

      // Time restrictions check
      if (!this.checkTimeRestrictions(signal, reasons)) {
        score -= 10;
      }

      // Signal frequency check
      if (!this.checkSignalFrequency(signal, reasons)) {
        score -= 10;
      }

      // Blacklist check
      if (!this.checkBlacklist(signal, reasons)) {
        score -= 50;
      }

      // Methodology confluence check
      if (
        enhancedAnalysis &&
        !this.checkMethodologyConfluence(enhancedAnalysis, reasons)
      ) {
        score -= 20;
      }

      const passed = score >= 60 && reasons.length === 0;
      const recommendation = this.getRecommendation(score, reasons.length);

      // Store signal in history
      this.addToHistory(signal);

      const result: FilterResult = {
        passed,
        reasons,
        score: Math.max(0, score),
        recommendation
      };

      logger.info(
        'signals',
        `Signal filtering completed for ${signal.symbol}`,
        {
          signalId: signal.id,
          passed,
          score: result.score,
          reasonCount: reasons.length,
          recommendation
        }
      );

      return result;
    } catch (error) {
      logger.error('signals', 'Error filtering signal', error);
      return {
        passed: false,
        reasons: ['Internal filtering error'],
        score: 0,
        recommendation: 'reject'
      };
    }
  }

  /**
   * Validate basic signal properties
   */
  private validateBasicSignal(
    signal: AdvancedSignal,
    reasons: string[]
  ): boolean {
    let valid = true;

    if (!signal.id || !signal.symbol || !signal.type) {
      reasons.push('Missing required signal properties');
      valid = false;
    }

    if (
      signal.entryPrice <= 0 ||
      signal.stopLoss <= 0 ||
      signal.takeProfit <= 0
    ) {
      reasons.push('Invalid price levels');
      valid = false;
    }

    if (signal.type === 'BUY' && signal.stopLoss >= signal.entryPrice) {
      reasons.push('Invalid stop loss for BUY signal');
      valid = false;
    }

    if (signal.type === 'SELL' && signal.stopLoss <= signal.entryPrice) {
      reasons.push('Invalid stop loss for SELL signal');
      valid = false;
    }

    return valid;
  }

  /**
   * Check confidence threshold
   */
  private checkConfidenceThreshold(
    signal: AdvancedSignal,
    reasons: string[]
  ): boolean {
    if (signal.confidence < this.config.minConfidence) {
      reasons.push(
        `Confidence ${signal.confidence.toFixed(1)}% below minimum ${
          this.config.minConfidence
        }%`
      );
      return false;
    }
    return true;
  }

  /**
   * Check risk assessment
   */
  private checkRiskAssessment(
    riskAssessment: RiskAssessment,
    reasons: string[]
  ): boolean {
    const riskLevels = ['low', 'moderate', 'high', 'very_high'];
    const maxRiskIndex = riskLevels.indexOf(this.config.maxRiskLevel);
    const currentRiskIndex = riskLevels.indexOf(riskAssessment.level);

    if (currentRiskIndex > maxRiskIndex) {
      reasons.push(
        `Risk level ${riskAssessment.level} exceeds maximum ${this.config.maxRiskLevel}`
      );
      return false;
    }

    if (riskAssessment.score < 40) {
      reasons.push(`Risk score ${riskAssessment.score.toFixed(1)} too low`);
      return false;
    }

    return true;
  }

  /**
   * Check risk/reward ratio
   */
  private checkRiskRewardRatio(
    signal: AdvancedSignal,
    reasons: string[]
  ): boolean {
    const rrRatio = signal.riskRewardRatio || 0;

    if (rrRatio < this.config.riskRewardRatio.minimum) {
      reasons.push(
        `Risk/Reward ratio ${rrRatio.toFixed(2)} below minimum ${
          this.config.riskRewardRatio.minimum
        }`
      );
      return false;
    }

    return true;
  }

  /**
   * Check market conditions
   */
  private checkMarketConditions(marketData: any, reasons: string[]): boolean {
    let valid = true;

    if (
      marketData.spread &&
      marketData.spread > this.config.marketConditions.maxSpread
    ) {
      reasons.push(
        `Spread ${marketData.spread} exceeds maximum ${this.config.marketConditions.maxSpread}`
      );
      valid = false;
    }

    if (
      marketData.volume &&
      marketData.volume < this.config.marketConditions.minVolume
    ) {
      reasons.push(
        `Volume ${marketData.volume} below minimum ${this.config.marketConditions.minVolume}`
      );
      valid = false;
    }

    if (
      marketData.volatility &&
      marketData.volatility > this.config.marketConditions.maxVolatility
    ) {
      reasons.push(
        `Volatility ${marketData.volatility} exceeds maximum ${this.config.marketConditions.maxVolatility}`
      );
      valid = false;
    }

    return valid;
  }

  /**
   * Check time restrictions
   */
  private checkTimeRestrictions(
    signal: AdvancedSignal,
    reasons: string[]
  ): boolean {
    const signalTime = new Date(signal.timestamp);
    let valid = true;

    // Check weekend restriction
    if (this.config.timeRestrictions.avoidWeekends) {
      const dayOfWeek = signalTime.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        reasons.push('Signal generated during weekend');
        valid = false;
      }
    }

    // Check trading hours
    const hours = signalTime.getHours();
    const minutes = signalTime.getMinutes();
    const currentTime = hours * 60 + minutes;

    const [startHour, startMin] =
      this.config.timeRestrictions.tradingHours.start.split(':').map(Number);
    const [endHour, endMin] = this.config.timeRestrictions.tradingHours.end
      .split(':')
      .map(Number);
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (currentTime < startTime || currentTime > endTime) {
      reasons.push('Signal generated outside trading hours');
      valid = false;
    }

    return valid;
  }

  /**
   * Check signal frequency
   */
  private checkSignalFrequency(
    signal: AdvancedSignal,
    reasons: string[]
  ): boolean {
    const history = this.signalHistory.get(signal.symbol) || [];
    const oneHourAgo = signal.timestamp - 60 * 60 * 1000;
    const recentSignals = history.filter((s) => s.timestamp > oneHourAgo);

    if (recentSignals.length >= this.config.maxSignalsPerHour) {
      reasons.push(
        `Too many signals for ${signal.symbol} in the last hour (${recentSignals.length}/${this.config.maxSignalsPerHour})`
      );
      return false;
    }

    return true;
  }

  /**
   * Check blacklist
   */
  private checkBlacklist(signal: AdvancedSignal, reasons: string[]): boolean {
    if (this.config.blacklistPairs.includes(signal.symbol)) {
      reasons.push(`Symbol ${signal.symbol} is blacklisted`);
      return false;
    }
    return true;
  }

  /**
   * Check methodology confluence
   */
  private checkMethodologyConfluence(
    enhancedAnalysis: EnhancedSignalAnalysis,
    reasons: string[]
  ): boolean {
    const methodologyCount = Object.keys(
      enhancedAnalysis.confluence.methodologyBreakdown || {}
    ).length;

    if (methodologyCount < this.config.requiredMethodologies) {
      reasons.push(
        `Insufficient methodology agreement (${methodologyCount}/${this.config.requiredMethodologies})`
      );
      return false;
    }

    if (enhancedAnalysis.confluence.agreementRatio < 0.6) {
      reasons.push(
        `Low methodology agreement ratio (${(
          enhancedAnalysis.confluence.agreementRatio * 100
        ).toFixed(1)}%)`
      );
      return false;
    }

    return true;
  }

  /**
   * Get recommendation based on score and issues
   */
  private getRecommendation(
    score: number,
    issueCount: number
  ): 'accept' | 'review' | 'reject' {
    if (score >= 80 && issueCount === 0) return 'accept';
    if (score >= 60 && issueCount <= 2) return 'review';
    return 'reject';
  }

  /**
   * Add signal to history
   */
  private addToHistory(signal: AdvancedSignal): void {
    if (!this.signalHistory.has(signal.symbol)) {
      this.signalHistory.set(signal.symbol, []);
    }

    const history = this.signalHistory.get(signal.symbol)!;
    history.push(signal);

    // Clean old signals
    const cutoffTime = signal.timestamp - this.maxHistoryHours * 60 * 60 * 1000;
    const filteredHistory = history.filter((s) => s.timestamp > cutoffTime);
    this.signalHistory.set(signal.symbol, filteredHistory);
  }

  /**
   * Update filter configuration
   */
  public updateConfig(newConfig: Partial<SignalFilterConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('signals', 'Signal filter configuration updated');
  }

  /**
   * Get current configuration
   */
  public getConfig(): SignalFilterConfig {
    return { ...this.config };
  }

  /**
   * Get filtering statistics
   */
  public getFilteringStats(): any {
    const totalSymbols = this.signalHistory.size;
    const totalSignals = Array.from(this.signalHistory.values()).reduce(
      (sum, signals) => sum + signals.length,
      0
    );

    return {
      totalSymbols,
      totalSignals,
      averageSignalsPerSymbol:
        totalSymbols > 0 ? totalSignals / totalSymbols : 0,
      historyHours: this.maxHistoryHours
    };
  }

  /**
   * Clear signal history
   */
  public clearHistory(symbol?: string): void {
    if (symbol) {
      this.signalHistory.delete(symbol);
      logger.info('signals', `Cleared signal history for ${symbol}`);
    } else {
      this.signalHistory.clear();
      logger.info('signals', 'Cleared all signal history');
    }
  }
}

// Export singleton instance
export const signalFilteringService = new SignalFilteringService();
