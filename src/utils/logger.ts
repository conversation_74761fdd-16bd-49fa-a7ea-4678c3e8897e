import fs from 'fs';
import path from 'path';
import { config } from '../config';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  service: string;
  message: string;
  data?: any;
}

export interface LogRotationConfig {
  enabled: boolean;
  retentionDays: number;
  baseDirectory: string;
}

class Logger {
  private logLevel: LogLevel;
  private logFilePath: string;
  private rotationConfig: LogRotationConfig;
  private currentLogDate: string;
  private lastCleanupDate: string;

  constructor() {
    this.logLevel = config.logging.level as LogLevel;
    this.logFilePath = config.logging.filePath;
    this.rotationConfig = {
      enabled: config.logging.rotation?.enabled ?? true,
      retentionDays: config.logging.rotation?.retentionDays ?? 30,
      baseDirectory: config.logging.rotation?.baseDirectory ?? './logs'
    };
    this.currentLogDate = this.getCurrentDateString();
    this.lastCleanupDate = '';
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    const logDir = this.rotationConfig.enabled
      ? this.rotationConfig.baseDirectory
      : path.dirname(this.logFilePath);

    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  private getCurrentDateString(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${day}-${month}-${year}`;
  }

  private getCurrentLogFilePath(): string {
    if (!this.rotationConfig.enabled) {
      return this.logFilePath;
    }

    const currentDate = this.getCurrentDateString();
    return path.join(this.rotationConfig.baseDirectory, `${currentDate}.log`);
  }

  private checkAndRotateLog(): void {
    if (!this.rotationConfig.enabled) {
      return;
    }

    const currentDate = this.getCurrentDateString();
    if (currentDate !== this.currentLogDate) {
      this.currentLogDate = currentDate;
      this.ensureLogDirectory();

      // Perform cleanup if it's a new day and we haven't cleaned up today
      if (this.lastCleanupDate !== currentDate) {
        this.cleanupOldLogs();
        this.lastCleanupDate = currentDate;
      }
    }
  }

  private cleanupOldLogs(): void {
    if (
      !this.rotationConfig.enabled ||
      this.rotationConfig.retentionDays <= 0
    ) {
      return;
    }

    try {
      const logDir = this.rotationConfig.baseDirectory;
      if (!fs.existsSync(logDir)) {
        return;
      }

      const files = fs.readdirSync(logDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(
        cutoffDate.getDate() - this.rotationConfig.retentionDays
      );

      for (const file of files) {
        if (!file.endsWith('.log')) {
          continue;
        }

        // Extract date from filename (YYYY-MM-DD.log format)
        const dateMatch = file.match(/^(\d{4}-\d{2}-\d{2})\.log$/);
        if (!dateMatch) {
          continue;
        }

        const fileDate = new Date(dateMatch[1]);
        if (fileDate < cutoffDate) {
          const filePath = path.join(logDir, file);
          fs.unlinkSync(filePath);
          console.log(`Cleaned up old log file: ${file}`);
        }
      }
    } catch (error) {
      console.error('Error during log cleanup:', error);
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };
    return levels[level] >= levels[this.logLevel];
  }

  private formatMessage(entry: LogEntry): string {
    const dataStr = entry.data ? ` | Data: ${JSON.stringify(entry.data)}` : '';
    return `[${entry.timestamp}] [${entry.level.toUpperCase()}] [${
      entry.service
    }] ${entry.message}${dataStr}`;
  }

  private writeToFile(message: string): void {
    try {
      // Check if we need to rotate the log file
      this.checkAndRotateLog();

      // Get the current log file path (either rotated or static)
      const currentLogPath = this.getCurrentLogFilePath();

      fs.appendFileSync(currentLogPath, message + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private log(
    level: LogLevel,
    service: string,
    message: string,
    data?: any
  ): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      service,
      message,
      data
    };

    const formattedMessage = this.formatMessage(entry);

    // Console output with colors
    const colors = {
      debug: '\x1b[36m', // Cyan
      info: '\x1b[32m', // Green
      warn: '\x1b[33m', // Yellow
      error: '\x1b[31m' // Red
    };
    const reset = '\x1b[0m';

    console.log(`${colors[level]}${formattedMessage}${reset}`);

    // File output
    this.writeToFile(formattedMessage);
  }

  debug(service: string, message: string, data?: any): void {
    this.log('debug', service, message, data);
  }

  info(service: string, message: string, data?: any): void {
    this.log('info', service, message, data);
  }

  warn(service: string, message: string, data?: any): void {
    this.log('warn', service, message, data);
  }

  error(service: string, message: string, data?: any): void {
    this.log('error', service, message, data);
  }

  // Convenience methods for specific services
  trading(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'TRADING', message, data);
  }

  websocket(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'WEBSOCKET', message, data);
  }

  telegram(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'TELEGRAM', message, data);
  }

  indicators(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'INDICATORS', message, data);
  }

  patterns(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'PATTERNS', message, data);
  }

  database(level: LogLevel, message: string, data?: any): void {
    this.log(level, 'DATABASE', message, data);
  }

  // Utility methods for log management
  getLogRotationConfig(): LogRotationConfig {
    return { ...this.rotationConfig };
  }

  getCurrentLogFile(): string {
    return this.getCurrentLogFilePath();
  }

  forceLogRotation(): void {
    if (this.rotationConfig.enabled) {
      this.currentLogDate = ''; // Force rotation on next log
      this.checkAndRotateLog();
    }
  }

  manualCleanup(): void {
    this.cleanupOldLogs();
  }

  getLogStats(): {
    currentLogFile: string;
    rotationEnabled: boolean;
    retentionDays: number;
    currentDate: string;
  } {
    return {
      currentLogFile: this.getCurrentLogFilePath(),
      rotationEnabled: this.rotationConfig.enabled,
      retentionDays: this.rotationConfig.retentionDays,
      currentDate: this.currentLogDate
    };
  }
}

export const logger = new Logger();
export default logger;
